'use client';

import { useEffect, useRef } from 'react';
import { craneLoadingAnimation } from '../../lib/animations';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const craneRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (craneRef.current) {
      craneLoadingAnimation(craneRef.current);
    }
  }, []);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div 
      className={`loading-container flex items-center justify-center ${className}`}
      data-testid="loading-spinner"
    >
      <div 
        ref={craneRef}
        className={`crane-loading ${sizeClasses[size]} relative`}
        data-testid="crane-animation"
      >
        {/* Crane SVG */}
        <svg 
          viewBox="0 0 100 100" 
          className="w-full h-full text-primary"
          data-testid="crane-svg"
        >
          {/* Crane base */}
          <rect x="45" y="85" width="10" height="10" fill="currentColor" />
          
          {/* Crane mast */}
          <rect x="48" y="20" width="4" height="65" fill="currentColor" />
          
          {/* Crane jib (horizontal arm) */}
          <rect x="20" y="18" width="60" height="4" fill="currentColor" />
          
          {/* Counter jib */}
          <rect x="10" y="18" width="15" height="4" fill="currentColor" />
          
          {/* Hook and load */}
          <g className="crane-hook">
            <line x1="70" y1="22" x2="70" y2="40" stroke="currentColor" strokeWidth="2" />
            <rect x="65" y="40" width="10" height="8" fill="currentColor" className="crane-load" />
          </g>
          
          {/* Counterweight */}
          <rect x="8" y="15" width="8" height="10" fill="currentColor" />
        </svg>
        
        {/* Loading text */}
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-sm text-base-content/70 whitespace-nowrap">
          Loading...
        </div>
      </div>
    </div>
  );
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

export function LoadingOverlay({ isVisible, message = 'Loading...', className = '' }: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div 
      className={`fixed inset-0 bg-base-100/80 backdrop-blur-sm flex items-center justify-center z-50 ${className}`}
      data-testid="loading-overlay"
    >
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-lg text-base-content/80">{message}</p>
      </div>
    </div>
  );
}
