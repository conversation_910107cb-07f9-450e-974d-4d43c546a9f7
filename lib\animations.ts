import { animate, stagger } from 'motion';

// Navbar crane animation variants
export const craneAnimations = {
  // Initial state - crane at rest
  initial: {
    transform: 'rotate(0deg) translateY(0px)',
    opacity: 1,
  },
  
  // Lifting animation - crane rotates and lifts
  lifting: {
    transform: 'rotate(-15deg) translateY(-5px)',
    opacity: 0.8,
  },
  
  // Working animation - crane is actively working
  working: {
    transform: 'rotate(-25deg) translateY(-10px)',
    opacity: 0.6,
  },
};

// Scroll-triggered crane animation
export const animateCraneOnScroll = (element: Element, scrollY: number) => {
  const maxScroll = 300; // Maximum scroll distance for full animation
  const progress = Math.min(scrollY / maxScroll, 1);

  // Calculate rotation and translation based on scroll progress
  const rotation = -25 * progress; // Rotate up to -25 degrees
  const translateY = -10 * progress; // Move up by 10px
  const opacity = 1 - (0.4 * progress); // Fade to 60% opacity

  animate(
    element,
    {
      rotate: rotation,
      y: translateY,
      opacity: opacity,
    },
    {
      duration: 0.3,
      easing: 'ease-out',
    }
  );
};

// Navbar background animation on scroll
export const animateNavbarOnScroll = (element: Element, scrollY: number) => {
  const threshold = 50; // Scroll threshold for navbar background change

  if (scrollY > threshold) {
    // Add a class for styling instead of direct style manipulation
    element.classList.add('navbar-scrolled');
  } else {
    element.classList.remove('navbar-scrolled');
  }
};

// Page transition animations
export const pageTransitions = {
  enter: {
    opacity: 0,
    y: 20,
  },

  enterActive: {
    opacity: 1,
    y: 0,
  },

  exit: {
    opacity: 1,
    y: 0,
  },

  exitActive: {
    opacity: 0,
    y: -20,
  },
};

// Animate page transitions
export const animatePageTransition = (element: Element, direction: 'enter' | 'exit') => {
  if (direction === 'enter') {
    return animate(
      element,
      [pageTransitions.enter, pageTransitions.enterActive],
      {
        duration: 0.5,
        easing: 'ease-out',
      }
    );
  } else {
    return animate(
      element,
      [pageTransitions.exit, pageTransitions.exitActive],
      {
        duration: 0.3,
        easing: 'ease-in',
      }
    );
  }
};

// Hover animations for interactive elements
export const hoverAnimations = {
  // Card hover effect
  cardHover: {
    scale: 1.05,
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
  },
  
  // Button hover effect
  buttonHover: {
    scale: 1.02,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  
  // Crane card specific hover
  craneCardHover: {
    scale: 1.03,
    y: -5,
  },
};

// Apply hover animation
export const animateHover = (
  element: Element, 
  variant: keyof typeof hoverAnimations,
  isHovering: boolean
) => {
  const animation = hoverAnimations[variant];
  
  if (isHovering) {
    animate(element, animation, {
      duration: 0.2,
      easing: 'ease-out',
    });
  } else {
    animate(element, {
      scale: 1,
      y: 0,
    }, {
      duration: 0.2,
      easing: 'ease-out',
    });
  }
};

// Parallax animation
export const animateParallax = (element: Element, scrollY: number, speed: number = 0.5) => {
  const translateY = scrollY * speed;

  animate(
    element,
    {
      y: translateY,
    },
    {
      duration: 0,
    }
  );
};

// Loading animation for crane theme
export const craneLoadingAnimation = (element: Element) => {
  // Create a sequence of animations
  const animateSequence = async () => {
    await animate(
      element,
      {
        rotate: -30,
        y: -15,
      },
      { duration: 1, easing: 'ease-in-out' }
    ).finished;

    await animate(
      element,
      {
        rotate: 30,
        y: -15,
      },
      { duration: 1, easing: 'ease-in-out' }
    ).finished;

    await animate(
      element,
      {
        rotate: 0,
        y: 0,
      },
      { duration: 1, easing: 'ease-in-out' }
    ).finished;

    // Repeat the animation
    craneLoadingAnimation(element);
  };

  animateSequence();
};

// Staggered animation for multiple elements
export const animateStagger = (elements: Element[], delay: number = 0.1) => {
  return animate(
    elements,
    {
      opacity: [0, 1],
      y: [20, 0],
    },
    {
      duration: 0.5,
      delay: stagger(delay),
      easing: 'ease-out',
    }
  );
};
