import { useTranslations } from 'next-intl';
import { CompanyInfo, TeamMember } from '../lib/data';
import { TeamProfileCard } from './TeamProfileCard';

interface CompanyInfoSectionProps {
  companyInfo: CompanyInfo;
  teamMembers: TeamMember[];
}

export function CompanyInfoSection({ companyInfo, teamMembers }: CompanyInfoSectionProps) {
  const t = useTranslations();

  return (
    <section className="py-16 px-4 bg-base-100" data-testid="company-info-section">
      <div className="container mx-auto">
        {/* Company Information */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-base-content mb-4">
              {t('company.title')}
            </h2>
            <h3 className="text-2xl font-semibold text-primary mb-6">
              {companyInfo.name}
            </h3>
            <p className="text-base-content/80 max-w-3xl mx-auto">
              {companyInfo.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {/* Statistics */}
            <div className="stats shadow">
              <div className="stat">
                <div className="stat-title">{t('company.founded')}</div>
                <div className="stat-value text-primary">{companyInfo.founded}</div>
              </div>
            </div>
            
            <div className="stats shadow">
              <div className="stat">
                <div className="stat-title">{t('company.employees')}</div>
                <div className="stat-value text-primary">{companyInfo.employees}</div>
              </div>
            </div>
            
            <div className="stats shadow">
              <div className="stat">
                <div className="stat-title">{t('company.projects')}</div>
                <div className="stat-value text-primary">{companyInfo.projectsCompleted}</div>
              </div>
            </div>
          </div>
          
          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <h4 className="font-semibold text-base-content mb-2">
                {t('company.address')}
              </h4>
              <p className="text-base-content/70 text-sm">
                {companyInfo.address}
              </p>
            </div>
            
            <div className="text-center">
              <h4 className="font-semibold text-base-content mb-2">
                {t('company.phone')}
              </h4>
              <p className="text-base-content/70 text-sm">
                {companyInfo.phone}
              </p>
            </div>
            
            <div className="text-center">
              <h4 className="font-semibold text-base-content mb-2">
                {t('company.email')}
              </h4>
              <p className="text-base-content/70 text-sm">
                {companyInfo.email}
              </p>
            </div>
          </div>
        </div>
        
        {/* Team Section */}
        <div>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-base-content mb-4">
              {t('team.title')}
            </h2>
            <p className="text-base-content/70 max-w-2xl mx-auto">
              {t('team.subtitle')}
            </p>
          </div>
          
          <div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            data-testid="team-grid"
          >
            {teamMembers.map((member) => (
              <TeamProfileCard key={member.id} member={member} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
