import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeToggle } from './ThemeToggle';

// Mock the useThemeStorage hook
jest.mock('../lib/hooks', () => ({
  useThemeStorage: jest.fn(),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'theme.toggle': 'Toggle theme',
      'theme.light': 'Light theme',
      'theme.dark': 'Dark theme',
    };
    return translations[key] || key;
  },
}));

import { useThemeStorage } from '../lib/hooks';

const mockUseThemeStorage = useThemeStorage as jest.MockedFunction<typeof useThemeStorage>;

describe('ThemeToggle', () => {
  const mockSetTheme = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset DOM
    document.documentElement.removeAttribute('data-theme');
  });

  it('should render toggle button', () => {
    mockUseThemeStorage.mockReturnValue(['light', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    const toggle = screen.getByRole('checkbox');
    expect(toggle).toBeInTheDocument();
  });

  it('should display current theme state', () => {
    mockUseThemeStorage.mockReturnValue(['light', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    const toggle = screen.getByRole('checkbox') as HTMLInputElement;
    expect(toggle.checked).toBe(true); // light theme should be checked
  });

  it('should show unchecked when night theme is active', () => {
    mockUseThemeStorage.mockReturnValue(['night', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    const toggle = screen.getByRole('checkbox') as HTMLInputElement;
    expect(toggle.checked).toBe(false); // night theme should be unchecked
  });

  it('should toggle theme when clicked', () => {
    mockUseThemeStorage.mockReturnValue(['light', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);
    
    expect(mockSetTheme).toHaveBeenCalledWith('night');
  });

  it('should toggle from night to light theme', () => {
    mockUseThemeStorage.mockReturnValue(['night', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);
    
    expect(mockSetTheme).toHaveBeenCalledWith('light');
  });

  it('should set data-theme attribute on document', () => {
    mockUseThemeStorage.mockReturnValue(['night', mockSetTheme]);
    
    render(<ThemeToggle />);
    
    expect(document.documentElement.getAttribute('data-theme')).toBe('night');
  });
}); 