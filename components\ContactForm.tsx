'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

interface FormData {
  inquiryType: 'rental' | 'purchase' | 'sales';
  name: string;
  email: string;
  phone: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
}

export function ContactForm() {
  const t = useTranslations('contact');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<FormData>({
    inquiryType: 'rental',
    name: '',
    email: '',
    phone: '',
    message: '',
  });

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    console.log('Validating form with data:', formData);

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      console.log('Name validation failed');
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      console.log('Email validation failed - empty');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
      console.log('Email validation failed - invalid format');
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
      console.log('Message validation failed');
    }

    console.log('Setting errors:', newErrors);
    setErrors(newErrors);
    console.log('Validation result:', Object.keys(newErrors).length === 0);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleInquiryTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      inquiryType: e.target.value as 'rental' | 'purchase' | 'sales',
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = validateForm();
    if (!isValid) {

      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear form on success
      setFormData({
        inquiryType: 'rental',
        name: '',
        email: '',
        phone: '',
        message: '',
      });

      // Show success message (could be implemented with toast)

    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} role="form" className="space-y-6">
      {/* Inquiry Type */}
      <div className="form-control">
        <label className="label">
          <span className="label-text font-semibold">Inquiry Type</span>
        </label>
        <div className="flex flex-wrap gap-4">
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="rental"
              checked={formData.inquiryType === 'rental'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('rental')}</span>
          </label>
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="purchase"
              checked={formData.inquiryType === 'purchase'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('purchase')}</span>
          </label>
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="sales"
              checked={formData.inquiryType === 'sales'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('sales')}</span>
          </label>
        </div>
      </div>

      {/* Name */}
      <div className="form-control">
        <label className="label" htmlFor="name">
          <span className="label-text">{t('name')} *</span>
        </label>
        <input
          id="name"
          name="name"
          type="text"
          value={formData.name}
          onChange={handleInputChange}
          required
          className={`input input-bordered w-full ${errors.name ? 'input-error' : ''}`}
          placeholder="Enter your full name"
        />
        {errors.name && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.name}</span>
          </label>
        )}
      </div>

      {/* Email */}
      <div className="form-control">
        <label className="label" htmlFor="email">
          <span className="label-text">{t('email')} *</span>
        </label>
        <input
          id="email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          className={`input input-bordered w-full ${errors.email ? 'input-error' : ''}`}
          placeholder="Enter your email address"
        />
        {errors.email && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.email}</span>
          </label>
        )}
      </div>

      {/* Phone */}
      <div className="form-control">
        <label className="label" htmlFor="phone">
          <span className="label-text">{t('phone')}</span>
        </label>
        <input
          id="phone"
          name="phone"
          type="tel"
          value={formData.phone}
          onChange={handleInputChange}
          className={`input input-bordered w-full ${errors.phone ? 'input-error' : ''}`}
          placeholder="Enter your phone number (optional)"
        />
        {errors.phone && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.phone}</span>
          </label>
        )}
      </div>

      {/* Message */}
      <div className="form-control">
        <label className="label" htmlFor="message">
          <span className="label-text">{t('message')} *</span>
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleInputChange}
          required
          rows={5}
          className={`textarea textarea-bordered w-full ${errors.message ? 'textarea-error' : ''}`}
          placeholder="Describe your crane requirements, project details, timeline, etc."
        />
        {errors.message && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.message}</span>
          </label>
        )}
      </div>

      {/* Submit Button */}
      <div className="form-control">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`btn btn-primary w-full ${isSubmitting ? 'loading' : ''}`}
        >
          {isSubmitting ? 'Submitting...' : t('submit')}
        </button>
      </div>
    </form>
  );
}

export default ContactForm;
