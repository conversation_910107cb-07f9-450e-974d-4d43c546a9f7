'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { submitContactForm, type ContactFormData, type ContactFormResult } from '../app/actions/contact';

interface FormData {
  inquiryType: 'rental' | 'purchase' | 'sales';
  name: string;
  email: string;
  phone: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
}

export function ContactForm() {
  const t = useTranslations('contact');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<FormData>({
    inquiryType: 'rental',
    name: '',
    email: '',
    phone: '',
    message: '',
  });
  const [submitResult, setSubmitResult] = useState<ContactFormResult | null>(null);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleInquiryTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      inquiryType: e.target.value as 'rental' | 'purchase' | 'sales',
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous submit result
    setSubmitResult(null);

    const isValid = validateForm();
    if (!isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit form using server action
      const result = await submitContactForm(formData as ContactFormData);

      setSubmitResult(result);

      if (result.success) {
        // Clear form on success
        setFormData({
          inquiryType: 'rental',
          name: '',
          email: '',
          phone: '',
          message: '',
        });
        setErrors({});
      } else if (result.errors) {
        // Set server-side validation errors
        setErrors(result.errors);
      }

    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitResult({
        success: false,
        message: 'There was an error submitting your inquiry. Please try again later.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} role="form" className="space-y-6">
      {/* Success/Error Message */}
      {submitResult && (
        <div className={`alert ${submitResult.success ? 'alert-success' : 'alert-error'}`}>
          <span>{submitResult.message}</span>
        </div>
      )}

      {/* Inquiry Type */}
      <div className="form-control">
        <label className="label">
          <span className="label-text font-semibold">Inquiry Type</span>
        </label>
        <div className="flex flex-wrap gap-4">
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="rental"
              checked={formData.inquiryType === 'rental'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('rental')}</span>
          </label>
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="purchase"
              checked={formData.inquiryType === 'purchase'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('purchase')}</span>
          </label>
          <label className="label cursor-pointer">
            <input
              type="radio"
              name="inquiryType"
              value="sales"
              checked={formData.inquiryType === 'sales'}
              onChange={handleInquiryTypeChange}
              className="radio radio-primary"
            />
            <span className="label-text ml-2">{t('sales')}</span>
          </label>
        </div>
      </div>

      {/* Name */}
      <div className="form-control">
        <label className="label" htmlFor="name">
          <span className="label-text">{t('name')} *</span>
        </label>
        <input
          id="name"
          name="name"
          type="text"
          value={formData.name}
          onChange={handleInputChange}
          required
          className={`input input-bordered w-full ${errors.name ? 'input-error' : ''}`}
          placeholder="Enter your full name"
        />
        {errors.name && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.name}</span>
          </label>
        )}
      </div>

      {/* Email */}
      <div className="form-control">
        <label className="label" htmlFor="email">
          <span className="label-text">{t('email')} *</span>
        </label>
        <input
          id="email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          className={`input input-bordered w-full ${errors.email ? 'input-error' : ''}`}
          placeholder="Enter your email address"
        />
        {errors.email && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.email}</span>
          </label>
        )}
      </div>

      {/* Phone */}
      <div className="form-control">
        <label className="label" htmlFor="phone">
          <span className="label-text">{t('phone')}</span>
        </label>
        <input
          id="phone"
          name="phone"
          type="tel"
          value={formData.phone}
          onChange={handleInputChange}
          className={`input input-bordered w-full ${errors.phone ? 'input-error' : ''}`}
          placeholder="Enter your phone number (optional)"
        />
        {errors.phone && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.phone}</span>
          </label>
        )}
      </div>

      {/* Message */}
      <div className="form-control">
        <label className="label" htmlFor="message">
          <span className="label-text">{t('message')} *</span>
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleInputChange}
          required
          rows={5}
          className={`textarea textarea-bordered w-full ${errors.message ? 'textarea-error' : ''}`}
          placeholder="Describe your crane requirements, project details, timeline, etc."
        />
        {errors.message && (
          <label className="label">
            <span className="label-text-alt text-error">{errors.message}</span>
          </label>
        )}
      </div>

      {/* Submit Button */}
      <div className="form-control">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`btn btn-primary w-full ${isSubmitting ? 'loading' : ''}`}
        >
          {isSubmitting ? 'Submitting...' : t('submit')}
        </button>
      </div>
    </form>
  );
}

export default ContactForm;
