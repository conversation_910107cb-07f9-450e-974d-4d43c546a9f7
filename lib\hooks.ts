import { useState, useEffect } from 'react';

export function useThemeStorage(defaultTheme: string): [string, (theme: string) => void] {
  const [theme, setThemeState] = useState<string>(defaultTheme);

  useEffect(() => {
    try {
      const storedTheme = localStorage.getItem('theme');
      if (storedTheme) {
        setThemeState(storedTheme);
      }
    } catch (error) {
      // Handle localStorage errors gracefully
      console.warn('Failed to read theme from localStorage:', error);
    }
  }, []);

  const setTheme = (newTheme: string) => {
    try {
      localStorage.setItem('theme', newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
      setThemeState(newTheme);
    }
  };

  return [theme, setTheme];
} 