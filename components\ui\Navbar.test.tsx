import { render, screen, fireEvent } from '@testing-library/react';
import { Navbar } from './Navbar';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'nav.home': 'Home',
      'nav.cranes': 'Cranes',
      'nav.projects': 'Projects',
      'nav.about': 'About',
      'nav.contact': 'Contact',
      'nav.menu': 'Menu',
      'nav.close': 'Close',
    };
    return translations[key] || key;
  },
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ href, children, ...props }: { href: string; children: React.ReactNode; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock ThemeToggle component
jest.mock('../ThemeToggle', () => ({
  ThemeToggle: () => <div data-testid="theme-toggle">Theme Toggle</div>
}));

// Mock animation functions
jest.mock('../../lib/animations', () => ({
  animateCraneOnScroll: jest.fn(),
  animateNavbarOnScroll: jest.fn(),
}));

describe('Navbar', () => {
  it('should render navbar with brand logo', () => {
    render(<Navbar />);
    
    const navbar = screen.getByRole('navigation');
    expect(navbar).toBeInTheDocument();
    
    const brandLink = screen.getByRole('link', { name: /crane portfolio/i });
    expect(brandLink).toBeInTheDocument();
    expect(brandLink).toHaveAttribute('href', '/');
  });

  it('should render all navigation links', () => {
    render(<Navbar />);

    const homeLinks = screen.getAllByRole('link', { name: 'home' });
    const cranesLinks = screen.getAllByRole('link', { name: 'cranes' });
    const projectsLinks = screen.getAllByRole('link', { name: 'projects' });
    const aboutLinks = screen.getAllByRole('link', { name: 'about' });
    const contactLinks = screen.getAllByRole('link', { name: 'contact' });

    expect(homeLinks.length).toBeGreaterThan(0);
    expect(cranesLinks.length).toBeGreaterThan(0);
    expect(projectsLinks.length).toBeGreaterThan(0);
    expect(aboutLinks.length).toBeGreaterThan(0);
    expect(contactLinks.length).toBeGreaterThan(0);
  });

  it('should have correct href attributes for navigation links', () => {
    render(<Navbar />);

    const homeLinks = screen.getAllByRole('link', { name: 'home' });
    const cranesLinks = screen.getAllByRole('link', { name: 'cranes' });
    const projectsLinks = screen.getAllByRole('link', { name: 'projects' });
    const aboutLinks = screen.getAllByRole('link', { name: 'about' });
    const contactLinks = screen.getAllByRole('link', { name: 'contact' });

    expect(homeLinks[0]).toHaveAttribute('href', '/');
    expect(cranesLinks[0]).toHaveAttribute('href', '/cranes');
    expect(projectsLinks[0]).toHaveAttribute('href', '/projects');
    expect(aboutLinks[0]).toHaveAttribute('href', '/about');
    expect(contactLinks[0]).toHaveAttribute('href', '/contact');
  });

  it('should render theme toggle component', () => {
    render(<Navbar />);

    const themeToggles = screen.getAllByTestId('theme-toggle');
    expect(themeToggles.length).toBeGreaterThan(0);
    expect(themeToggles[0]).toBeInTheDocument();
  });

  it('should render mobile menu toggle button', () => {
    render(<Navbar />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveClass('lg:hidden');
  });

  it('should toggle mobile menu when menu button is clicked', () => {
    render(<Navbar />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    const mobileMenu = screen.getByTestId('mobile-menu');
    
    // Initially hidden
    expect(mobileMenu).toHaveClass('hidden');
    
    // Click to show
    fireEvent.click(menuButton);
    expect(mobileMenu).not.toHaveClass('hidden');
    
    // Click to hide
    fireEvent.click(menuButton);
    expect(mobileMenu).toHaveClass('hidden');
  });

  it('should have crane-themed styling classes', () => {
    render(<Navbar />);

    const navbar = screen.getByRole('navigation');
    expect(navbar).toHaveClass('navbar');
    expect(navbar).toHaveClass('bg-base-100');
    expect(navbar).toHaveClass('shadow-lg');
    expect(navbar).toHaveClass('fixed');
    expect(navbar).toHaveClass('top-0');
    expect(navbar).toHaveClass('z-50');
  });

  it('should render crane icon in brand', () => {
    render(<Navbar />);
    
    const craneIcon = screen.getByTestId('crane-icon');
    expect(craneIcon).toBeInTheDocument();
  });

  it('should have responsive navigation menu', () => {
    render(<Navbar />);
    
    const desktopMenu = screen.getByTestId('desktop-menu');
    const mobileMenu = screen.getByTestId('mobile-menu');
    
    expect(desktopMenu).toHaveClass('hidden', 'lg:flex');
    expect(mobileMenu).toHaveClass('lg:hidden');
  });

  it('should close mobile menu when navigation link is clicked', () => {
    render(<Navbar />);

    const menuButton = screen.getByRole('button', { name: /menu/i });
    const mobileMenu = screen.getByTestId('mobile-menu');

    // Open mobile menu
    fireEvent.click(menuButton);
    expect(mobileMenu).not.toHaveClass('hidden');

    // Click a navigation link in mobile menu
    const mobileHomeLink = mobileMenu.querySelector('a[href="/"]');
    if (mobileHomeLink) {
      fireEvent.click(mobileHomeLink);
      expect(mobileMenu).toHaveClass('hidden');
    }
  });

  it('should set up scroll event listener for animations', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

    const { unmount } = render(<Navbar />);

    // Check that scroll event listener was added
    expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: true });

    // Unmount component
    unmount();

    // Check that scroll event listener was removed
    expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function));

    addEventListenerSpy.mockRestore();
    removeEventListenerSpy.mockRestore();
  });

  it('should have crane icon with animation classes', () => {
    render(<Navbar />);

    const craneIcon = screen.getByTestId('crane-icon');
    expect(craneIcon).toHaveClass('transition-transform');
    expect(craneIcon).toHaveClass('duration-300');
    expect(craneIcon).toHaveClass('origin-bottom');
  });
});
