'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useThemeStorage } from '../lib/hooks';

export function ThemeToggle() {
  const t = useTranslations('theme');
  const [theme, setTheme] = useThemeStorage('night');

  useEffect(() => {
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    const newTheme = theme === 'night' ? 'light' : 'night';
    setTheme(newTheme);
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-base-content/70">
        {theme === 'night' ? t('dark') : t('light')}
      </span>
      <input
        type="checkbox"
        className="toggle toggle-primary"
        checked={theme === 'light'}
        onChange={toggleTheme}
        aria-label={t('toggle')}
      />
    </div>
  );
} 