import { submitContactForm, type ContactFormData } from './contact';

describe('submitContactForm', () => {
  const validFormData: ContactFormData = {
    inquiryType: 'rental',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    message: 'I need to rent a crane for my construction project.',
  };

  beforeEach(() => {
    // Clear console.log spy before each test
    jest.clearAllMocks();
    // Mock console.log to avoid cluttering test output
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log after each test
    jest.restoreAllMocks();
  });

  it('should successfully submit valid form data', async () => {
    const result = await submitContactForm(validFormData);

    expect(result.success).toBe(true);
    expect(result.message).toBe('Thank you for your inquiry! We will get back to you within 24 hours.');
    expect(result.errors).toBeUndefined();
  });

  it('should handle form data without phone number', async () => {
    const formDataWithoutPhone: ContactFormData = {
      ...validFormData,
      phone: undefined,
    };

    const result = await submitContactForm(formDataWithoutPhone);

    expect(result.success).toBe(true);
    expect(result.message).toBe('Thank you for your inquiry! We will get back to you within 24 hours.');
  });

  it('should validate required fields and return errors', async () => {
    const invalidFormData: ContactFormData = {
      inquiryType: 'rental',
      name: '',
      email: '',
      phone: '',
      message: '',
    };

    const result = await submitContactForm(invalidFormData);

    expect(result.success).toBe(false);
    expect(result.message).toBe('Please correct the errors below.');
    expect(result.errors).toBeDefined();
    expect(result.errors?.name).toBe('Name is required');
    expect(result.errors?.email).toBe('Please enter a valid email address');
    expect(result.errors?.message).toBe('Message is required');
  });

  it('should validate email format', async () => {
    const invalidEmailData: ContactFormData = {
      ...validFormData,
      email: 'invalid-email',
    };

    const result = await submitContactForm(invalidEmailData);

    expect(result.success).toBe(false);
    expect(result.message).toBe('Please correct the errors below.');
    expect(result.errors?.email).toBe('Please enter a valid email address');
  });

  it('should validate inquiry type', async () => {
    const invalidInquiryTypeData = {
      ...validFormData,
      inquiryType: 'invalid-type',
    } as ContactFormData;

    const result = await submitContactForm(invalidInquiryTypeData);

    expect(result.success).toBe(false);
    expect(result.message).toBe('Please correct the errors below.');
    expect(result.errors?.inquiryType).toBeDefined();
  });

  it('should handle different inquiry types', async () => {
    const inquiryTypes: Array<'rental' | 'purchase' | 'sales'> = ['rental', 'purchase', 'sales'];

    for (const inquiryType of inquiryTypes) {
      const formData: ContactFormData = {
        ...validFormData,
        inquiryType,
      };

      const result = await submitContactForm(formData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Thank you for your inquiry! We will get back to you within 24 hours.');
    }
  });

  it('should trim whitespace from string fields', async () => {
    const formDataWithWhitespace: ContactFormData = {
      inquiryType: 'rental',
      name: '  John Doe  ',
      email: '  <EMAIL>  ',
      phone: '  +1234567890  ',
      message: '  I need to rent a crane.  ',
    };

    const result = await submitContactForm(formDataWithWhitespace);

    expect(result.success).toBe(true);
    expect(result.message).toBe('Thank you for your inquiry! We will get back to you within 24 hours.');
  });

  it('should reject empty strings after trimming', async () => {
    const formDataWithOnlyWhitespace: ContactFormData = {
      inquiryType: 'rental',
      name: '   ',
      email: '   ',
      phone: '',
      message: '   ',
    };

    const result = await submitContactForm(formDataWithOnlyWhitespace);

    expect(result.success).toBe(false);
    expect(result.message).toBe('Please correct the errors below.');
    expect(result.errors?.name).toBe('Name is required');
    expect(result.errors?.email).toBe('Please enter a valid email address');
    expect(result.errors?.message).toBe('Message is required');
  });

  it('should handle long messages', async () => {
    const longMessage = 'A'.repeat(1000); // 1000 character message
    const formDataWithLongMessage: ContactFormData = {
      ...validFormData,
      message: longMessage,
    };

    const result = await submitContactForm(formDataWithLongMessage);

    expect(result.success).toBe(true);
    expect(result.message).toBe('Thank you for your inquiry! We will get back to you within 24 hours.');
  });

  it('should log submission details on success', async () => {
    const consoleSpy = jest.spyOn(console, 'log');

    await submitContactForm(validFormData);

    expect(consoleSpy).toHaveBeenCalledWith('Contact form submitted:', expect.objectContaining({
      inquiryType: 'rental',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      message: 'I need to rent a crane for my construction project.',
      timestamp: expect.any(String),
    }));
  });
});
