@import "tailwindcss";
@plugin "daisyui" {
  themes: night --default, light --prefersdark;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Navbar scroll animation styles */
.navbar-scrolled {
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-out;
}

/* Hover effects */
.hover-active {
  transform: scale(1.03) translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease-out;
}

/* Card hover effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

/* Button hover effects */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Crane loading animation */
.crane-loading {
  animation: craneWork 3s ease-in-out infinite;
}

@keyframes craneWork {
  0% { transform: rotate(0deg) translateY(0px); }
  33% { transform: rotate(-30deg) translateY(-15px); }
  66% { transform: rotate(30deg) translateY(-15px); }
  100% { transform: rotate(0deg) translateY(0px); }
}

@keyframes craneHoist {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes cranePulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.crane-hook {
  animation: craneHoist 1.5s ease-in-out infinite;
  transform-origin: 70px 22px;
}

.crane-load {
  animation: craneHoist 1.5s ease-in-out infinite, cranePulse 2s ease-in-out infinite;
}

/* Loading container */
.loading-container {
  position: relative;
}

.loading-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
  opacity: 0.3;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Page transition styles */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}


