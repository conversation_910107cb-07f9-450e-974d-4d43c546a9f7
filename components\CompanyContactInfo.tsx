import { useTranslations } from 'next-intl';

export function CompanyContactInfo() {
  const t = useTranslations('company');

  return (
    <div className="space-y-6">
      {/* Contact Information Card */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title text-2xl mb-6 text-base-content">
            {t('contactInfo')}
          </h2>

          <div className="space-y-4">
            {/* Address */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">📍</div>
              <div>
                <h3 className="font-semibold text-base-content">{t('address')}</h3>
                <p className="text-base-content/70">
                  Strada Constructorilor 123<br />
                  Sector 1, București 010101<br />
                  România
                </p>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">📞</div>
              <div>
                <h3 className="font-semibold text-base-content">{t('phone')}</h3>
                <p className="text-base-content/70">
                  <a href="tel:+40123456789" className="hover:text-primary transition-colors">
                    +40 123 456 789
                  </a>
                </p>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">✉️</div>
              <div>
                <h3 className="font-semibold text-base-content">{t('email')}</h3>
                <p className="text-base-content/70">
                  <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            {/* Business Hours */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">🕒</div>
              <div>
                <h3 className="font-semibold text-base-content">{t('businessHours')}</h3>
                <div className="text-base-content/70">
                  <p>{t('mondayFriday')}</p>
                  <p>{t('saturday')}</p>
                  <p>{t('sunday')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Contact Card */}
      <div className="card bg-error/10 border border-error/20">
        <div className="card-body">
          <h3 className="card-title text-error">
            🚨 {t('emergencyServices')}
          </h3>
          <p className="text-base-content/70 mb-4">
            {t('emergencyDescription')}
          </p>
          <div className="flex flex-col space-y-2">
            <a
              href="tel:+***********"
              className="btn btn-error btn-sm"
            >
              📞 {t('emergencyHotline')}: +**************
            </a>
          </div>
        </div>
      </div>

      {/* Services Overview */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title text-base-content mb-4">
            {t('ourServices')}
          </h3>
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center space-x-2">
              <div className="text-primary">🏗️</div>
              <span className="text-base-content/80">{t('craneRental')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">💰</div>
              <span className="text-base-content/80">{t('craneSales')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">🔧</div>
              <span className="text-base-content/80">{t('maintenance')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">👷</div>
              <span className="text-base-content/80">{t('operatorServices')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">📋</div>
              <span className="text-base-content/80">{t('consultation')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
