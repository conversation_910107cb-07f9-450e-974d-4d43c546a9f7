import { useTranslations } from 'next-intl';

export function CompanyContactInfo() {
  const t = useTranslations('company');

  return (
    <div className="space-y-6">
      {/* Contact Information Card */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title text-2xl mb-6 text-base-content">
            Contact Information
          </h2>
          
          <div className="space-y-4">
            {/* Address */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">📍</div>
              <div>
                <h3 className="font-semibold text-base-content">Address</h3>
                <p className="text-base-content/70">
                  Strada Constructorilor 123<br />
                  Sector 1, București 010101<br />
                  România
                </p>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">📞</div>
              <div>
                <h3 className="font-semibold text-base-content">Phone</h3>
                <p className="text-base-content/70">
                  <a href="tel:+40123456789" className="hover:text-primary transition-colors">
                    +40 123 456 789
                  </a>
                </p>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">✉️</div>
              <div>
                <h3 className="font-semibold text-base-content">Email</h3>
                <p className="text-base-content/70">
                  <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            {/* Business Hours */}
            <div className="flex items-start space-x-3">
              <div className="text-primary text-xl">🕒</div>
              <div>
                <h3 className="font-semibold text-base-content">Business Hours</h3>
                <div className="text-base-content/70">
                  <p>Monday - Friday: 8:00 AM - 6:00 PM</p>
                  <p>Saturday: 9:00 AM - 4:00 PM</p>
                  <p>Sunday: Closed</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Contact Card */}
      <div className="card bg-error/10 border border-error/20">
        <div className="card-body">
          <h3 className="card-title text-error">
            🚨 Emergency Services
          </h3>
          <p className="text-base-content/70 mb-4">
            24/7 emergency crane services available
          </p>
          <div className="flex flex-col space-y-2">
            <a 
              href="tel:+***********" 
              className="btn btn-error btn-sm"
            >
              📞 Emergency Hotline: +**************
            </a>
          </div>
        </div>
      </div>

      {/* Services Overview */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title text-base-content mb-4">
            Our Services
          </h3>
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center space-x-2">
              <div className="text-primary">🏗️</div>
              <span className="text-base-content/80">Crane Rental</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">💰</div>
              <span className="text-base-content/80">Crane Sales</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">🔧</div>
              <span className="text-base-content/80">Maintenance & Repair</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">👷</div>
              <span className="text-base-content/80">Operator Services</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-primary">📋</div>
              <span className="text-base-content/80">Project Consultation</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
