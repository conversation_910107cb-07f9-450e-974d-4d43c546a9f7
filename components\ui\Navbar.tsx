'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { ThemeToggle } from '../ThemeToggle';
import { animateCraneOnScroll, animateNavbarOnScroll } from '../../lib/animations';

export function Navbar() {
  const t = useTranslations('nav');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navbarRef = useRef<HTMLElement>(null);
  const craneIconRef = useRef<HTMLDivElement>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Scroll animation effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;

      if (navbarRef.current) {
        animateNavbarOnScroll(navbarRef.current, scrollY);
      }

      if (craneIconRef.current) {
        animateCraneOnScroll(craneIconRef.current, scrollY);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const navigationLinks = [
    { href: '/', label: t('home') },
    { href: '/cranes', label: t('cranes') },
    { href: '/projects', label: t('projects') },
    { href: '/about', label: t('about') },
    { href: '/contact', label: t('contact') },
  ];

  return (
    <nav
      ref={navbarRef}
      className="navbar bg-base-100 shadow-lg fixed top-0 left-0 right-0 z-50 transition-all duration-300"
      role="navigation"
    >
      <div className="container mx-auto">
        {/* Brand */}
        <div className="navbar-start">
          <Link href="/" className="btn btn-ghost text-xl font-bold">
            <div
              ref={craneIconRef}
              data-testid="crane-icon"
              className="w-8 h-8 mr-2 transition-transform duration-300 origin-bottom"
            >
              {/* Crane SVG Icon */}
              <svg
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-full h-full"
              >
                <path d="M3 21h18" />
                <path d="M5 21V7l8-4v18" />
                <path d="M19 21V11l-6-4" />
                <path d="M9 9h10" />
                <path d="M9 15h6" />
              </svg>
            </div>
            Crane Portfolio
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="navbar-center">
          <ul 
            className="menu menu-horizontal px-1 hidden lg:flex" 
            data-testid="desktop-menu"
          >
            {navigationLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href} className="btn btn-ghost">
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Theme Toggle & Mobile Menu Button */}
        <div className="navbar-end">
          <div className="hidden lg:block mr-4">
            <ThemeToggle />
          </div>
          
          {/* Mobile Menu Button */}
          <button
            className="btn btn-ghost lg:hidden"
            onClick={toggleMobileMenu}
            aria-label={isMobileMenuOpen ? t('close') : t('menu')}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div 
        className={`lg:hidden ${isMobileMenuOpen ? '' : 'hidden'}`}
        data-testid="mobile-menu"
      >
        <div className="px-4 pt-2 pb-4 bg-base-100 shadow-lg">
          <ul className="menu menu-vertical w-full">
            {navigationLinks.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="btn btn-ghost justify-start"
                  onClick={closeMobileMenu}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
          
          <div className="mt-4 pt-4 border-t border-base-300">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </nav>
  );
}
