import { render, screen } from '@testing-library/react';
import { CraneCard } from './CraneCard';
import { Crane } from '../lib/data';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'crane.specifications': 'Specifications',
      'crane.capacity': 'Capacity',
      'crane.height': 'Height',
      'crane.boom': 'Boom Length',
      'crane.available': 'Available',
      'crane.unavailable': 'Unavailable',
      'home.viewDetails': 'View Details',
    };
    return translations[key] || key;
  },
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: unknown }) {
    return <div data-testid="mock-image" data-src={src} data-alt={alt} {...props} />;
  };
});

const mockCrane: Crane = {
  id: 'test-crane-001',
  name: 'Test Liebherr LTM 1200-5.1',
  type: 'Mobile Crane',
  specifications: {
    capacity: '200 tons',
    height: '84 m',
    boom: '72 m',
    weight: '60 tons',
    year: 2022
  },
  description: 'High-performance mobile crane for testing purposes.',
  imageUrl: '/images/cranes/test-crane.jpg',
  available: true,
  price: {
    rental: '€2,500/day',
    purchase: '€1,200,000'
  }
};

const mockUnavailableCrane: Crane = {
  ...mockCrane,
  id: 'test-crane-002',
  name: 'Test Unavailable Crane',
  available: false
};

describe('CraneCard', () => {
  it('renders crane name correctly', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('Test Liebherr LTM 1200-5.1')).toBeInTheDocument();
  });

  it('renders crane type correctly', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('Mobile Crane')).toBeInTheDocument();
  });

  it('renders crane description correctly', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('High-performance mobile crane for testing purposes.')).toBeInTheDocument();
  });

  it('renders crane image with correct alt text', () => {
    render(<CraneCard crane={mockCrane} />);

    const image = screen.getByTestId('mock-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('data-src', '/images/cranes/test-crane.jpg');
    expect(image).toHaveAttribute('data-alt', 'Test Liebherr LTM 1200-5.1');
  });

  it('renders crane specifications correctly', () => {
    render(<CraneCard crane={mockCrane} />);

    expect(screen.getByText('Specifications')).toBeInTheDocument();
    expect(screen.getByText(/Capacity/)).toBeInTheDocument();
    expect(screen.getByText('200 tons')).toBeInTheDocument();
    expect(screen.getByText(/Height/)).toBeInTheDocument();
    expect(screen.getByText('84 m')).toBeInTheDocument();
    expect(screen.getByText(/Boom Length/)).toBeInTheDocument();
    expect(screen.getByText('72 m')).toBeInTheDocument();
  });

  it('shows available status for available crane', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('Available')).toBeInTheDocument();
  });

  it('shows unavailable status for unavailable crane', () => {
    render(<CraneCard crane={mockUnavailableCrane} />);
    
    expect(screen.getByText('Unavailable')).toBeInTheDocument();
  });

  it('renders rental price when available', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('€2,500/day')).toBeInTheDocument();
  });

  it('renders view details button', () => {
    render(<CraneCard crane={mockCrane} />);
    
    expect(screen.getByText('View Details')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View Details' })).toBeInTheDocument();
  });

  it('applies correct CSS classes for card structure', () => {
    render(<CraneCard crane={mockCrane} />);
    
    const card = screen.getByTestId('crane-card');
    expect(card).toHaveClass('card');
  });

  it('applies available styling when crane is available', () => {
    render(<CraneCard crane={mockCrane} />);
    
    const statusBadge = screen.getByTestId('availability-status');
    expect(statusBadge).toHaveClass('badge-success');
  });

  it('applies unavailable styling when crane is unavailable', () => {
    render(<CraneCard crane={mockUnavailableCrane} />);
    
    const statusBadge = screen.getByTestId('availability-status');
    expect(statusBadge).toHaveClass('badge-error');
  });
});
