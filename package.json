{"name": "crane-portfolio-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"motion": "^12.19.1", "next": "15.3.4", "next-intl": "^4.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^30.0.2", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.3", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.2", "tailwindcss": "^4", "typescript": "^5"}}