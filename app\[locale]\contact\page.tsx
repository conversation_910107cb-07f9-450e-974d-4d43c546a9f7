import { useTranslations } from 'next-intl';
import ContactForm from '../../../components/ContactForm';
import { CompanyContactInfo } from '../../../components/CompanyContactInfo';

export default function ContactPage() {
  const t = useTranslations('contact');

  return (
    <div className="min-h-screen bg-base-100">
      {/* Hero Section */}
      <section className="hero bg-base-200 py-20">
        <div className="hero-content text-center">
          <div className="max-w-md">
            <h1 className="text-5xl font-bold text-base-content">
              {t('title')}
            </h1>
            <p className="py-6 text-lg text-base-content/70">
              {t('subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Contact Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="order-2 lg:order-1">
              <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                  <h2 className="card-title text-2xl mb-6 text-base-content">
                    Send us a message
                  </h2>
                  <ContactForm />
                </div>
              </div>
            </div>

            {/* Company Contact Information */}
            <div className="order-1 lg:order-2">
              <CompanyContactInfo />
            </div>
          </div>
        </div>
      </section>

      {/* Map Section (Placeholder) */}
      <section className="py-16 bg-base-200">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-base-content mb-4">
              Find Us
            </h2>
            <p className="text-base-content/70">
              Visit our location for equipment viewing and consultations
            </p>
          </div>
          
          {/* Map placeholder */}
          <div className="bg-base-300 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">📍</div>
              <p className="text-base-content/70">
                Interactive map will be integrated here
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
