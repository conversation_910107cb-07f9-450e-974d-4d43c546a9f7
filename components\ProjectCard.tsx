import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Project } from '../lib/data';

interface ProjectCardProps {
  project: Project;
}

export function ProjectCard({ project }: ProjectCardProps) {
  const t = useTranslations();

  // Format the completion date to show month and year
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    });
  };

  return (
    <div className="card bg-base-100 shadow-xl" data-testid="project-card">
      <figure className="px-4 pt-4">
        <Image
          src={project.imageUrl}
          alt={project.title}
          width={400}
          height={300}
          className="rounded-xl object-cover w-full h-48"
        />
      </figure>
      
      <div className="card-body">
        <h3 className="card-title text-base-content">
          {project.title}
        </h3>
        
        <p className="text-base-content/80 mb-4">{project.description}</p>
        
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="font-medium text-base-content/70">
              {t('project.location')}:
            </span>
            <span className="text-base-content">
              {project.location}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="font-medium text-base-content/70">
              {t('project.completed')}:
            </span>
            <span className="text-base-content">
              {formatDate(project.completedDate)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="font-medium text-base-content/70">
              {t('project.craneUsed')}:
            </span>
            <span className="text-base-content">
              {project.craneUsed}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="font-medium text-base-content/70">
              {t('project.client')}:
            </span>
            <span className="text-base-content">
              {project.clientName}
            </span>
          </div>
        </div>
        
        <div className="card-actions justify-end">
          <button className="btn btn-primary btn-sm">
            {t('project.viewDetails')}
          </button>
        </div>
      </div>
    </div>
  );
}
