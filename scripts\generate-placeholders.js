const fs = require('fs');
const path = require('path');

// Create a simple SVG placeholder generator
function createSVGPlaceholder(width, height, text, bgColor = '#e5e7eb', textColor = '#6b7280') {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${bgColor}"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
}

// Define placeholder images needed
const placeholders = [
  // Cranes
  { path: 'public/images/cranes/liebherr-ltm-1200.jpg', text: 'Liebherr LTM 1200', width: 400, height: 300 },
  { path: 'public/images/cranes/sany-stc-800s.jpg', text: 'SANY STC 800S', width: 400, height: 300 },
  { path: 'public/images/cranes/terex-rt-780.jpg', text: 'Terex RT 780', width: 400, height: 300 },
  { path: 'public/images/cranes/tadano-atf-130g.jpg', text: 'Tadano ATF 130G', width: 400, height: 300 },
  { path: 'public/images/cranes/manitowoc-mlc-650.jpg', text: 'Manitowoc MLC 650', width: 400, height: 300 },
  { path: 'public/images/cranes/grove-gmk-4100l.jpg', text: 'Grove GMK 4100L', width: 400, height: 300 },
  
  // Projects
  { path: 'public/images/projects/bucharest-sky-tower.jpg', text: 'Bucharest Sky Tower', width: 400, height: 300 },
  { path: 'public/images/projects/bridge-construction.jpg', text: 'Bridge Construction', width: 400, height: 300 },
  { path: 'public/images/projects/industrial-plant.jpg', text: 'Industrial Plant', width: 400, height: 300 },
  { path: 'public/images/projects/wind-farm.jpg', text: 'Wind Farm Project', width: 400, height: 300 },
  
  // Team
  { path: 'public/images/team/alexandru-popescu.jpg', text: 'Alexandru Popescu', width: 300, height: 300 },
  { path: 'public/images/team/elena-radu.jpg', text: 'Elena Radu', width: 300, height: 300 },
  { path: 'public/images/team/maria-ionescu.jpg', text: 'Maria Ionescu', width: 300, height: 300 },
  { path: 'public/images/team/gheorghe-dumitrescu.jpg', text: 'Gheorghe Dumitrescu', width: 300, height: 300 },
];

// Generate placeholder images
placeholders.forEach(({ path: filePath, text, width, height }) => {
  const dir = path.dirname(filePath);
  
  // Ensure directory exists
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Create SVG content
  const svgContent = createSVGPlaceholder(width, height, text);
  
  // Write SVG file (we'll use SVG instead of JPG for simplicity)
  const svgPath = filePath.replace('.jpg', '.svg');
  fs.writeFileSync(svgPath, svgContent);
  
  console.log(`Created placeholder: ${svgPath}`);
});

console.log('All placeholder images created successfully!');
