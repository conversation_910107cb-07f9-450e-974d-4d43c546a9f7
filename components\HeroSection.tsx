'use client';

import { useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { animateParallax } from '../lib/animations';

export function HeroSection() {
  const t = useTranslations();
  const heroRef = useRef<HTMLElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      
      if (backgroundRef.current) {
        animateParallax(backgroundRef.current, scrollY, 0.5);
      }
      
      if (contentRef.current) {
        animateParallax(contentRef.current, scrollY, 0.3);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section 
      ref={heroRef}
      className="hero min-h-screen relative overflow-hidden"
      data-testid="hero-section"
    >
      {/* Parallax Background */}
      <div 
        ref={backgroundRef}
        className="hero-background absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20"
        data-testid="hero-background"
      >
        <div className="absolute inset-0 bg-base-200/50"></div>
        {/* Crane silhouette background */}
        <div className="absolute right-0 top-0 w-1/2 h-full opacity-10">
          <svg 
            viewBox="0 0 400 600" 
            className="w-full h-full"
            data-testid="crane-silhouette"
          >
            <path 
              d="M50 550 L50 100 L350 100 L350 50 L370 50 L370 550 M50 100 L200 250 L350 100" 
              stroke="currentColor" 
              strokeWidth="8" 
              fill="none"
            />
            <circle cx="200" cy="250" r="15" fill="currentColor" />
            <rect x="45" y="540" width="10" height="20" fill="currentColor" />
            <rect x="365" y="540" width="10" height="20" fill="currentColor" />
          </svg>
        </div>
      </div>

      {/* Hero Content */}
      <div 
        ref={contentRef}
        className="hero-content text-center relative z-10"
        data-testid="hero-content"
      >
        <div className="max-w-md">
          <h1 className="mb-5 text-5xl font-bold text-base-content">
            {t('home.heroTitle')}
          </h1>
          <p className="mb-5 text-lg text-base-content/80">
            {t('home.heroSubtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn btn-primary btn-lg">
              {t('home.viewCranes')}
            </button>
            <button className="btn btn-outline btn-lg">
              {t('home.contactUs')}
            </button>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-base-content/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-base-content/30 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
