# Product Requirements Document: Crane Portfolio Website

## Introduction/Overview

This document outlines the requirements for developing a modern, responsive crane portfolio website using Next.js. The website serves as a presentation platform for a crane rental/sales company, allowing construction industry professionals and crane manufacturers to browse available equipment, view specifications, and initiate contact for rental, purchase, or sale inquiries. The site will feature innovative crane-themed animations and support multiple languages, starting with English and Romanian.

## Goals

1. **Primary Goal**: Create a professional presentation website that showcases crane inventory and facilitates business inquiries for rental, purchase, and sales
2. **User Experience**: Deliver an engaging, intuitive browsing experience with innovative crane-themed animations
3. **Accessibility**: Support multiple languages (English/Romanian initially) with easy expansion capability
4. **Performance**: Achieve fast loading times and smooth animations across all devices
5. **Lead Generation**: Facilitate easy contact and inquiry submission for potential customers
6. **Brand Differentiation**: Stand out from competitors through unique crane-themed UI/UX design

## User Stories

### Construction Industry Professionals
- **US1**: As a construction project manager, I want to browse available cranes by type and specifications so that I can find equipment suitable for my project needs
- **US2**: As a site foreman, I want to view high-quality images and videos of cranes in operation so that I can assess their condition and capabilities
- **US3**: As a procurement specialist, I want to easily contact the company with rental inquiries so that I can get quotes and availability information
- **US4**: As a Romanian-speaking contractor, I want to view the website in Romanian so that I can understand all details clearly

### Crane Manufacturers/Dealers
- **US5**: As a crane dealer, I want to view the company's inventory and services so that I can explore potential partnership opportunities
- **US6**: As a manufacturer representative, I want to see case studies and project portfolios so that I can understand the company's experience and capabilities
- **US7**: As a business development manager, I want to access company information and team profiles so that I can identify the right contacts for collaboration

## Functional Requirements

### Core Functionality
1. **FR1**: The system must display a responsive homepage showcasing featured cranes and company overview
2. **FR2**: The system must provide a dedicated contact page with inquiry forms for rental, purchase, and sales
3. **FR3**: The system must support internationalization (i18n) with English and Romanian languages
4. **FR4**: The system must include a language toggle that preserves user preference across sessions
5. **FR5**: The system must implement dark/light theme toggle with user preference persistence

### Content Management
6. **FR6**: The system must display crane specifications including technical details, capacity, and dimensions
7. **FR7**: The system must support high-quality image galleries for each crane type
8. **FR8**: The system must embed video content showing cranes in operation
9. **FR9**: The system must showcase project case studies and company portfolio
10. **FR10**: The system must display company information and team profiles
11. **FR11**: The system must include a blog/news section for industry updates and company news

### User Interface & Animations
12. **FR12**: The system must implement a crane-themed navigation bar that visually represents a crane structure
13. **FR13**: The system must create scroll-triggered animations where the navbar "crane" pulls content up as users scroll
14. **FR14**: The system must include smooth page transitions and loading animations
15. **FR15**: The system must provide hover effects on crane images and interactive elements
16. **FR16**: The system must implement parallax effects for hero sections

### Contact & Communication
17. **FR17**: The system must provide separate contact forms for rental inquiries, purchase requests, and sales submissions
18. **FR18**: The system must validate form inputs and provide user feedback on submission
19. **FR19**: The system must send email notifications to administrators upon form submission
20. **FR20**: The system must display company contact information including phone, email, and address

### Technical Requirements
21. **FR21**: The system must be built with Next.js 14+ using the App Router
22. **FR22**: The system must use Tailwind CSS 4+ for styling
23. **FR23**: The system must integrate DaisyUI components for consistent UI elements
24. **FR24**: The system must implement Framer Motion for all animations and transitions
25. **FR25**: The system must be fully responsive across desktop, tablet, and mobile devices
26. **FR26**: The system must achieve Lighthouse performance score of 90+ on all pages

## Non-Goals (Out of Scope)

1. **NG1**: E-commerce functionality (shopping cart, payment processing, online transactions)
2. **NG2**: User authentication and account management systems
3. **NG3**: Real-time inventory management or booking systems
4. **NG4**: Content Management System (CMS) integration for non-technical users
5. **NG5**: Additional pages beyond Home and Contact (these will be added in future phases)
6. **NG6**: Advanced search and filtering capabilities (Phase 2 feature)
7. **NG7**: Live chat functionality
8. **NG8**: Social media integration beyond basic links

## Design Considerations

### Visual Design
- **Professional/Industrial Aesthetic**: Clean, modern design with industrial elements
- **Color Scheme**: Dark theme as primary with optional light theme toggle
- **Typography**: Sans-serif fonts that convey professionalism and readability
- **Imagery**: High-quality crane photography with consistent style and quality
- **White Space**: Generous use of white space for clean, uncluttered layouts

### Crane-Themed UI Elements
- **Navigation**: Navbar designed to resemble crane structure with boom, mast, and counterweight
- **Scroll Animation**: Content appears to be "lifted" by the crane as users scroll
- **Loading States**: Crane-themed loading animations (e.g., crane hook lifting content)
- **Hover Effects**: Subtle crane-related micro-interactions

### Responsive Design
- **Mobile-First**: Optimized for mobile experience with progressive enhancement
- **Breakpoints**: Standard Tailwind breakpoints (sm, md, lg, xl, 2xl)
- **Touch Interactions**: Optimized for touch devices with appropriate touch targets

## Technical Considerations

### Architecture
- **Framework**: Next.js 14+ with App Router for modern React features
- **Styling**: Tailwind CSS 4+ with DaisyUI component library
- **Animations**: Framer Motion for performant animations
- **Internationalization**: next-intl for robust i18n support
- **Deployment**: Vercel or similar platform for optimal Next.js performance

### Performance
- **Image Optimization**: Next.js Image component with proper sizing and lazy loading
- **Code Splitting**: Automatic code splitting with Next.js
- **Bundle Analysis**: Regular bundle size monitoring and optimization
- **Caching**: Implement appropriate caching strategies for static content

### SEO & Accessibility
- **Meta Tags**: Dynamic meta tags for each page and language
- **Structured Data**: Schema.org markup for crane equipment
- **ARIA Labels**: Proper accessibility attributes for interactive elements
- **Keyboard Navigation**: Full keyboard accessibility support

## Success Metrics

### Performance Metrics
1. **Page Load Speed**: < 3 seconds for initial page load
2. **Lighthouse Score**: 90+ across Performance, Accessibility, Best Practices, and SEO
3. **Core Web Vitals**: Meet Google's recommended thresholds

### User Engagement
4. **Bounce Rate**: < 40% for homepage visitors
5. **Session Duration**: Average session > 2 minutes
6. **Page Views**: Average 3+ pages per session

### Business Metrics
7. **Inquiry Conversion**: 5%+ of visitors submit contact forms
8. **Language Usage**: Track Romanian vs English usage to inform future language additions
9. **Mobile Usage**: Track mobile vs desktop usage patterns

## Open Questions

1. **Content Management**: How will crane inventory and specifications be updated? Should we plan for a simple JSON-based system or database integration?

2. **Email Integration**: What email service should be used for form submissions? (e.g., EmailJS, Resend, Nodemailer with SMTP)

3. **Analytics**: Should we integrate Google Analytics or another analytics platform for tracking user behavior?

4. **Image/Video Hosting**: Where will crane images and videos be hosted? (CDN, Next.js public folder, external service)

5. **Contact Form Destinations**: What email addresses should receive the different types of inquiries (rental, purchase, sales)?

6. **Future Scalability**: Should the architecture be designed to easily accommodate additional pages like Services, Projects, or About in the future?

7. **Crane Data Structure**: What specific crane specifications should be displayed? (weight capacity, boom length, height, etc.)

8. **Browser Support**: What browsers need to be supported? Should we include polyfills for older browsers?

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Next Review**: Upon completion of clarifying questions 