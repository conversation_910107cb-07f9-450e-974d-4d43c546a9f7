import { render, screen } from '@testing-library/react';
import { CompanyInfoSection } from './CompanyInfoSection';
import { CompanyInfo, TeamMember } from '../lib/data';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'company.title': 'About Our Company',
      'company.founded': 'Founded',
      'company.employees': 'Employees',
      'company.projects': 'Projects Completed',
      'company.address': 'Address',
      'company.phone': 'Phone',
      'company.email': 'Email',
      'team.title': 'Our Team',
      'team.subtitle': 'Meet the professionals behind our success',
    };
    return translations[key] || key;
  },
}));

// Mock TeamProfileCard component
jest.mock('./TeamProfileCard', () => ({
  TeamProfileCard: ({ member }: { member: TeamMember }) => (
    <div data-testid={`team-card-${member.id}`}>
      {member.name}
    </div>
  ),
}));

const mockCompanyInfo: CompanyInfo = {
  name: 'Test Professional Crane Services',
  founded: '2008',
  employees: 45,
  projectsCompleted: 850,
  description: 'Leading test crane rental and services company in Romania.',
  address: 'Test Str. Industriei 123, Sector 3, Bucharest, Romania',
  phone: '+40 21 123 4567',
  email: '<EMAIL>'
};

const mockTeamMembers: TeamMember[] = [
  {
    id: 'test-team-001',
    name: 'Test Alexandru Popescu',
    position: 'Operations Manager',
    description: 'Test description',
    imageUrl: '/images/team/test-1.jpg',
    experience: '15+ years',
    email: '<EMAIL>'
  },
  {
    id: 'test-team-002',
    name: 'Test Maria Ionescu',
    position: 'Safety Coordinator',
    description: 'Test description',
    imageUrl: '/images/team/test-2.jpg',
    experience: '12+ years',
    email: '<EMAIL>'
  }
];

describe('CompanyInfoSection', () => {
  it('renders company section title correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('About Our Company')).toBeInTheDocument();
  });

  it('renders company name correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Test Professional Crane Services')).toBeInTheDocument();
  });

  it('renders company description correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Leading test crane rental and services company in Romania.')).toBeInTheDocument();
  });

  it('renders company statistics correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText(/Founded/)).toBeInTheDocument();
    expect(screen.getByText('2008')).toBeInTheDocument();
    expect(screen.getByText(/Employees/)).toBeInTheDocument();
    expect(screen.getByText('45')).toBeInTheDocument();
    expect(screen.getByText(/Projects Completed/)).toBeInTheDocument();
    expect(screen.getByText('850')).toBeInTheDocument();
  });

  it('renders company contact information correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText(/Address/)).toBeInTheDocument();
    expect(screen.getByText('Test Str. Industriei 123, Sector 3, Bucharest, Romania')).toBeInTheDocument();
    expect(screen.getByText(/Phone/)).toBeInTheDocument();
    expect(screen.getByText('+40 21 123 4567')).toBeInTheDocument();
    expect(screen.getByText(/Email/)).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders team section title correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Our Team')).toBeInTheDocument();
  });

  it('renders team section subtitle correctly', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Meet the professionals behind our success')).toBeInTheDocument();
  });

  it('renders all provided team members', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByTestId('team-card-test-team-001')).toBeInTheDocument();
    expect(screen.getByTestId('team-card-test-team-002')).toBeInTheDocument();
  });

  it('renders team member names within team cards', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Test Alexandru Popescu')).toBeInTheDocument();
    expect(screen.getByText('Test Maria Ionescu')).toBeInTheDocument();
  });

  it('applies correct CSS classes for section structure', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    const section = screen.getByTestId('company-info-section');
    expect(section).toHaveClass('py-16');
  });

  it('renders team members in a grid layout', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={mockTeamMembers} />);
    
    const grid = screen.getByTestId('team-grid');
    expect(grid).toHaveClass('grid');
  });

  it('handles empty team members array gracefully', () => {
    render(<CompanyInfoSection companyInfo={mockCompanyInfo} teamMembers={[]} />);
    
    expect(screen.getByText('Our Team')).toBeInTheDocument();
    expect(screen.getByTestId('team-grid')).toBeInTheDocument();
  });
});
