import { useTranslations } from 'next-intl';
import { CraneCard } from '../../components/CraneCard';
import { PortfolioSection } from '../../components/PortfolioSection';
import { CompanyInfoSection } from '../../components/CompanyInfoSection';
import { cranes, projects, teamMembers, companyInfo } from '../../lib/data';

export default function Home() {
  const t = useTranslations('home');

  // Get featured cranes (first 6 for homepage)
  const featuredCranes = cranes.slice(0, 6);

  return (
    <div className="min-h-screen bg-base-100">
      <div className="hero min-h-screen bg-base-200">
        <div className="hero-content text-center">
          <div className="max-w-md">
            <h1 className="text-5xl font-bold text-base-content">
              {t('title')}
            </h1>
            <p className="py-6 text-base-content/70">
              {t('subtitle')}
            </p>
            <button className="btn btn-primary">
              {t('contactUs')}
            </button>
          </div>
        </div>
      </div>

      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-base-content">
            {t('featuredCranes')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCranes.map((crane) => (
              <CraneCard key={crane.id} crane={crane} />
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <PortfolioSection projects={projects} />

      {/* Company and Team Information */}
      <CompanyInfoSection companyInfo={companyInfo} teamMembers={teamMembers} />
    </div>
  );
}