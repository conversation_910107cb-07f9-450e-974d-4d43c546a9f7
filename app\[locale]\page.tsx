import { useTranslations } from 'next-intl';

export default function Home() {
  const t = useTranslations('home');

  return (
    <div className="min-h-screen bg-base-100">
      <div className="hero min-h-screen bg-base-200">
        <div className="hero-content text-center">
          <div className="max-w-md">
            <h1 className="text-5xl font-bold text-base-content">
              {t('title')}
            </h1>
            <p className="py-6 text-base-content/70">
              {t('subtitle')}
            </p>
            <button className="btn btn-primary">
              {t('contactUs')}
            </button>
          </div>
        </div>
      </div>

      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-base-content">
            {t('featuredCranes')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Placeholder for crane cards */}
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h3 className="card-title">Sample Crane</h3>
                <p>Crane specifications will be displayed here.</p>
                <div className="card-actions justify-end">
                  <button className="btn btn-primary btn-sm">
                    {t('viewDetails')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 