import { getRequestConfig } from 'next-intl/server'
import { routing } from './routing'

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!routing.locales.includes(locale as any)) {
    throw new Error(`Invalid locale: ${locale}`)
  }

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default
  }
}) 