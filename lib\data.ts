// Types for the crane portfolio data
export interface CraneSpecifications {
  capacity: string;
  height: string;
  boom: string;
  weight: string;
  year: number;
}

export interface Crane {
  id: string;
  name: string;
  type: string;
  specifications: CraneSpecifications;
  description: string;
  imageUrl: string;
  available: boolean;
  price?: {
    rental: string;
    purchase: string;
  };
}

export interface Project {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  location: string;
  completedDate: string;
  craneUsed: string;
  clientName: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  description: string;
  imageUrl: string;
  experience: string;
  email: string;
}

export interface CompanyInfo {
  name: string;
  founded: string;
  employees: number;
  projectsCompleted: number;
  description: string;
  address: string;
  phone: string;
  email: string;
}

// Mock data for cranes
export const cranes: Crane[] = [
  {
    id: "crane-001",
    name: "Liebherr LTM 1200-5.1",
    type: "Mobile Crane",
    specifications: {
      capacity: "200 tons",
      height: "84 m",
      boom: "72 m",
      weight: "60 tons",
      year: 2022
    },
    description: "High-performance mobile crane ideal for heavy lifting operations in construction and industrial projects.",
    imageUrl: "/images/cranes/liebherr-ltm-1200.jpg",
    available: true,
    price: {
      rental: "€2,500/day",
      purchase: "€1,200,000"
    }
  },
  {
    id: "crane-002", 
    name: "Tadano ATF 130G-5",
    type: "All Terrain Crane",
    specifications: {
      capacity: "130 tons",
      height: "68 m",
      boom: "60 m",
      weight: "48 tons",
      year: 2021
    },
    description: "Versatile all-terrain crane perfect for urban construction sites with limited space.",
    imageUrl: "/images/cranes/tadano-atf-130g.jpg",
    available: true,
    price: {
      rental: "€1,800/day",
      purchase: "€850,000"
    }
  },
  {
    id: "crane-003",
    name: "Grove GMK 4100L-1",
    type: "Mobile Crane",
    specifications: {
      capacity: "100 tons",
      height: "56 m",
      boom: "50 m",
      weight: "36 tons",
      year: 2023
    },
    description: "Compact and efficient mobile crane designed for medium-duty lifting applications.",
    imageUrl: "/images/cranes/grove-gmk-4100l.jpg",
    available: false,
    price: {
      rental: "€1,200/day",
      purchase: "€650,000"
    }
  },
  {
    id: "crane-004",
    name: "Manitowoc MLC 650",
    type: "Crawler Crane",
    specifications: {
      capacity: "650 tons",
      height: "120 m",
      boom: "84 m",
      weight: "180 tons",
      year: 2020
    },
    description: "Heavy-duty crawler crane for large-scale construction and industrial projects.",
    imageUrl: "/images/cranes/manitowoc-mlc-650.jpg",
    available: true,
    price: {
      rental: "€4,500/day",
      purchase: "€2,800,000"
    }
  },
  {
    id: "crane-005",
    name: "Terex RT 780",
    type: "Rough Terrain Crane",
    specifications: {
      capacity: "80 tons",
      height: "48 m",
      boom: "43.3 m",
      weight: "32 tons",
      year: 2022
    },
    description: "Rugged rough terrain crane built for challenging outdoor construction environments.",
    imageUrl: "/images/cranes/terex-rt-780.jpg",
    available: true,
    price: {
      rental: "€1,000/day",
      purchase: "€480,000"
    }
  },
  {
    id: "crane-006",
    name: "Sany STC 800S",
    type: "Truck Crane",
    specifications: {
      capacity: "80 tons",
      height: "61 m",
      boom: "50 m",
      weight: "42 tons",
      year: 2023
    },
    description: "Modern truck crane with advanced safety features and excellent fuel efficiency.",
    imageUrl: "/images/cranes/sany-stc-800s.jpg",
    available: true,
    price: {
      rental: "€1,100/day",
      purchase: "€520,000"
    }
  }
];

// Mock data for projects
export const projects: Project[] = [
  {
    id: "project-001",
    title: "Bucharest Sky Tower Construction",
    description: "Major high-rise construction project in downtown Bucharest requiring heavy lifting capabilities for steel beam installation.",
    imageUrl: "/images/projects/bucharest-sky-tower.jpg",
    location: "Bucharest, Romania",
    completedDate: "2023-09-15",
    craneUsed: "Liebherr LTM 1200-5.1",
    clientName: "Sky Development Group"
  },
  {
    id: "project-002",
    title: "Industrial Plant Assembly",
    description: "Complete assembly of manufacturing equipment for automotive parts production facility.",
    imageUrl: "/images/projects/industrial-plant.jpg",
    location: "Cluj-Napoca, Romania",
    completedDate: "2023-07-22",
    craneUsed: "Manitowoc MLC 650",
    clientName: "AutoTech Industries"
  },
  {
    id: "project-003",
    title: "Bridge Construction Project",
    description: "Installation of precast concrete segments for highway bridge construction over the Danube River.",
    imageUrl: "/images/projects/bridge-construction.jpg",
    location: "Constanta, Romania",
    completedDate: "2023-11-08",
    craneUsed: "Tadano ATF 130G-5",
    clientName: "National Highway Authority"
  },
  {
    id: "project-004",
    title: "Wind Farm Installation",
    description: "Installation of wind turbine components for renewable energy project in rural area.",
    imageUrl: "/images/projects/wind-farm.jpg",
    location: "Dobrogea, Romania",
    completedDate: "2023-05-30",
    craneUsed: "Grove GMK 4100L-1",
    clientName: "Green Energy Solutions"
  }
];

// Mock data for team members
export const teamMembers: TeamMember[] = [
  {
    id: "team-001",
    name: "Alexandru Popescu",
    position: "Operations Manager",
    description: "Experienced operations manager with over 15 years in crane operations and project management.",
    imageUrl: "/images/team/alexandru-popescu.jpg",
    experience: "15+ years",
    email: "<EMAIL>"
  },
  {
    id: "team-002",
    name: "Maria Ionescu",
    position: "Safety Coordinator",
    description: "Certified safety professional ensuring all operations meet the highest safety standards.",
    imageUrl: "/images/team/maria-ionescu.jpg",
    experience: "12+ years",
    email: "<EMAIL>"
  },
  {
    id: "team-003",
    name: "Gheorghe Dumitrescu",
    position: "Senior Crane Operator",
    description: "Master crane operator with expertise in heavy-duty lifting operations and complex projects.",
    imageUrl: "/images/team/gheorghe-dumitrescu.jpg",
    experience: "20+ years",
    email: "<EMAIL>"
  },
  {
    id: "team-004",
    name: "Elena Radu",
    position: "Project Coordinator",
    description: "Skilled project coordinator managing client relationships and project timelines.",
    imageUrl: "/images/team/elena-radu.jpg",
    experience: "8+ years",
    email: "<EMAIL>"
  }
];

// Company information
export const companyInfo: CompanyInfo = {
  name: "Professional Crane Services",
  founded: "2008",
  employees: 45,
  projectsCompleted: 850,
  description: "Leading crane rental and services company in Romania, providing reliable solutions for construction, industrial, and infrastructure projects.",
  address: "Str. Industriei 123, Sector 3, Bucharest, Romania",
  phone: "+40 21 123 4567",
  email: "<EMAIL>"
};
