import { render, screen } from '@testing-library/react';
import { HeroSection } from './HeroSection';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'home.heroTitle': 'Professional Crane Services',
      'home.heroSubtitle': 'Reliable equipment for your construction needs',
      'home.viewCranes': 'View Our Cranes',
      'home.contactUs': 'Contact Us',
    };
    return translations[key] || key;
  },
}));

// Mock animation functions
jest.mock('../lib/animations', () => ({
  animateParallax: jest.fn(),
}));

describe('HeroSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render hero section with correct structure', () => {
    render(<HeroSection />);
    
    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
    expect(screen.getByTestId('hero-background')).toBeInTheDocument();
    expect(screen.getByTestId('hero-content')).toBeInTheDocument();
  });

  it('should display hero title and subtitle', () => {
    render(<HeroSection />);
    
    expect(screen.getByText('Professional Crane Services')).toBeInTheDocument();
    expect(screen.getByText('Reliable equipment for your construction needs')).toBeInTheDocument();
  });

  it('should render action buttons', () => {
    render(<HeroSection />);
    
    expect(screen.getByRole('button', { name: 'View Our Cranes' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Contact Us' })).toBeInTheDocument();
  });

  it('should have crane silhouette SVG', () => {
    render(<HeroSection />);
    
    expect(screen.getByTestId('crane-silhouette')).toBeInTheDocument();
  });

  it('should have correct CSS classes for hero section', () => {
    render(<HeroSection />);
    
    const heroSection = screen.getByTestId('hero-section');
    expect(heroSection).toHaveClass('hero');
    expect(heroSection).toHaveClass('min-h-screen');
    expect(heroSection).toHaveClass('relative');
    expect(heroSection).toHaveClass('overflow-hidden');
  });

  it('should set up scroll event listener for parallax effect', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
    
    const { unmount } = render(<HeroSection />);
    
    // Check that scroll event listener was added
    expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: true });
    
    // Unmount component
    unmount();
    
    // Check that scroll event listener was removed
    expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function));
    
    addEventListenerSpy.mockRestore();
    removeEventListenerSpy.mockRestore();
  });

  it('should have scroll indicator', () => {
    render(<HeroSection />);
    
    const scrollIndicator = screen.getByTestId('hero-section').querySelector('.animate-bounce');
    expect(scrollIndicator).toBeInTheDocument();
  });

  it('should have primary and outline buttons with correct classes', () => {
    render(<HeroSection />);
    
    const primaryButton = screen.getByRole('button', { name: 'View Our Cranes' });
    const outlineButton = screen.getByRole('button', { name: 'Contact Us' });
    
    expect(primaryButton).toHaveClass('btn-primary');
    expect(outlineButton).toHaveClass('btn-outline');
  });
});
