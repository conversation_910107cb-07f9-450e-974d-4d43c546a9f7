import { render, screen } from '@testing-library/react';
import { PortfolioSection } from './PortfolioSection';
import { Project } from '../lib/data';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'portfolio.title': 'Our Portfolio',
      'portfolio.subtitle': 'Successful projects completed with our crane services',
      'portfolio.viewAll': 'View All Projects',
    };
    return translations[key] || key;
  },
}));

// Mock ProjectCard component
jest.mock('./ProjectCard', () => ({
  ProjectCard: ({ project }: { project: Project }) => (
    <div data-testid={`project-card-${project.id}`}>
      {project.title}
    </div>
  ),
}));

const mockProjects: Project[] = [
  {
    id: 'test-project-001',
    title: 'Test Project 1',
    description: 'First test project',
    imageUrl: '/images/projects/test-1.jpg',
    location: 'Test City 1, Romania',
    completedDate: '2023-09-15',
    craneUsed: '<PERSON>bherr LTM 1200-5.1',
    clientName: 'Test Client 1'
  },
  {
    id: 'test-project-002',
    title: 'Test Project 2',
    description: 'Second test project',
    imageUrl: '/images/projects/test-2.jpg',
    location: 'Test City 2, Romania',
    completedDate: '2023-10-20',
    craneUsed: 'Tadano ATF 130G-5',
    clientName: 'Test Client 2'
  },
  {
    id: 'test-project-003',
    title: 'Test Project 3',
    description: 'Third test project',
    imageUrl: '/images/projects/test-3.jpg',
    location: 'Test City 3, Romania',
    completedDate: '2023-11-05',
    craneUsed: 'Grove GMK 4100L-1',
    clientName: 'Test Client 3'
  }
];

describe('PortfolioSection', () => {
  it('renders portfolio section title correctly', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    expect(screen.getByText('Our Portfolio')).toBeInTheDocument();
  });

  it('renders portfolio section subtitle correctly', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    expect(screen.getByText('Successful projects completed with our crane services')).toBeInTheDocument();
  });

  it('renders all provided projects', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    expect(screen.getByTestId('project-card-test-project-001')).toBeInTheDocument();
    expect(screen.getByTestId('project-card-test-project-002')).toBeInTheDocument();
    expect(screen.getByTestId('project-card-test-project-003')).toBeInTheDocument();
  });

  it('renders project titles within project cards', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    expect(screen.getByText('Test Project 1')).toBeInTheDocument();
    expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    expect(screen.getByText('Test Project 3')).toBeInTheDocument();
  });

  it('renders view all projects button when there are more than 6 projects', () => {
    const manyProjects = Array.from({ length: 8 }, (_, i) => ({
      ...mockProjects[0],
      id: `test-project-${i + 1}`,
      title: `Test Project ${i + 1}`
    }));

    render(<PortfolioSection projects={manyProjects} />);

    expect(screen.getByText('View All Projects')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View All Projects' })).toBeInTheDocument();
  });

  it('does not render view all projects button when there are 6 or fewer projects', () => {
    render(<PortfolioSection projects={mockProjects} />);

    expect(screen.queryByText('View All Projects')).not.toBeInTheDocument();
  });

  it('applies correct CSS classes for section structure', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    const section = screen.getByTestId('portfolio-section');
    expect(section).toHaveClass('py-16');
  });

  it('renders projects in a grid layout', () => {
    render(<PortfolioSection projects={mockProjects} />);
    
    const grid = screen.getByTestId('projects-grid');
    expect(grid).toHaveClass('grid');
  });

  it('handles empty projects array gracefully', () => {
    render(<PortfolioSection projects={[]} />);
    
    expect(screen.getByText('Our Portfolio')).toBeInTheDocument();
    expect(screen.getByTestId('projects-grid')).toBeInTheDocument();
  });

  it('limits displayed projects to maximum of 6', () => {
    const manyProjects = Array.from({ length: 10 }, (_, i) => ({
      ...mockProjects[0],
      id: `test-project-${i + 1}`,
      title: `Test Project ${i + 1}`
    }));
    
    render(<PortfolioSection projects={manyProjects} />);
    
    // Should only render first 6 projects
    expect(screen.getByTestId('project-card-test-project-1')).toBeInTheDocument();
    expect(screen.getByTestId('project-card-test-project-6')).toBeInTheDocument();
    expect(screen.queryByTestId('project-card-test-project-7')).not.toBeInTheDocument();
  });
});
