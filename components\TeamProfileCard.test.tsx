import { render, screen } from '@testing-library/react';
import { TeamProfileCard } from './TeamProfileCard';
import { TeamMember } from '../lib/data';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'team.experience': 'Experience',
      'team.contact': 'Contact',
    };
    return translations[key] || key;
  },
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: unknown }) {
    return <div data-testid="mock-image" data-src={src} data-alt={alt} {...props} />;
  };
});

const mockTeamMember: TeamMember = {
  id: 'test-team-001',
  name: '<PERSON> <PERSON><PERSON>',
  position: 'Test Operations Manager',
  description: 'Experienced test operations manager with over 15 years in crane operations.',
  imageUrl: '/images/team/test-alexandru.jpg',
  experience: '15+ years',
  email: '<EMAIL>'
};

describe('TeamProfileCard', () => {
  it('renders team member name correctly', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    expect(screen.getByText('Test Alexandru Popescu')).toBeInTheDocument();
  });

  it('renders team member position correctly', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    expect(screen.getByText('Test Operations Manager')).toBeInTheDocument();
  });

  it('renders team member description correctly', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    expect(screen.getByText('Experienced test operations manager with over 15 years in crane operations.')).toBeInTheDocument();
  });

  it('renders team member image with correct alt text', () => {
    render(<TeamProfileCard member={mockTeamMember} />);

    const image = screen.getByTestId('mock-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('data-src', '/images/team/test-alexandru.jpg');
    expect(image).toHaveAttribute('data-alt', 'Test Alexandru Popescu');
  });

  it('renders team member experience correctly', () => {
    render(<TeamProfileCard member={mockTeamMember} />);

    // Look for the specific experience label and value
    expect(screen.getByText('15+ years')).toBeInTheDocument();
    // Check that the experience section exists by looking for the specific structure
    const experienceSection = screen.getByText('15+ years').closest('div');
    expect(experienceSection).toBeInTheDocument();
  });

  it('renders team member email correctly', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders contact button', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    expect(screen.getByText('Contact')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Contact' })).toBeInTheDocument();
  });

  it('applies correct CSS classes for card structure', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    const card = screen.getByTestId('team-profile-card');
    expect(card).toHaveClass('card');
  });

  it('renders avatar image with circular styling', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    const avatar = screen.getByTestId('team-avatar');
    expect(avatar).toHaveClass('avatar');
  });

  it('email link has correct href attribute', () => {
    render(<TeamProfileCard member={mockTeamMember} />);
    
    const emailLink = screen.getByRole('link', { name: '<EMAIL>' });
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });
});
