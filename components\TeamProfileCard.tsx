'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState, useRef } from 'react';
import { TeamMember } from '../lib/data';
import { animateHover } from '../lib/animations';

interface TeamProfileCardProps {
  member: TeamMember;
}

export function TeamProfileCard({ member }: TeamProfileCardProps) {
  const t = useTranslations();
  const [, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (cardRef.current) {
      animateHover(cardRef.current, 'craneCardHover', true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (cardRef.current) {
      animateHover(cardRef.current, 'craneCardHover', false);
    }
  };

  return (
    <div
      ref={cardRef}
      className="card bg-base-100 shadow-xl transition-all duration-300 hover:shadow-2xl"
      data-testid="team-profile-card"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="card-body items-center text-center">
        <div className="avatar" data-testid="team-avatar">
          <div className="w-24 rounded-full">
            <Image
              src={member.imageUrl}
              alt={member.name}
              width={96}
              height={96}
              className="object-cover"
            />
          </div>
        </div>
        
        <h3 className="card-title text-base-content">
          {member.name}
        </h3>
        
        <p className="text-primary font-semibold mb-2">{member.position}</p>
        
        <p className="text-base-content/80 text-sm mb-4">{member.description}</p>
        
        <div className="mb-4">
          <div className="text-sm">
            <span className="font-medium text-base-content/70">
              {t('team.experience')}:
            </span>
            <span className="ml-1 text-base-content">
              {member.experience}
            </span>
          </div>
        </div>
        
        <div className="mb-4">
          <a 
            href={`mailto:${member.email}`}
            className="text-sm text-primary hover:text-primary-focus"
          >
            {member.email}
          </a>
        </div>
        
        <div className="card-actions">
          <button className="btn btn-primary btn-sm">
            {t('team.contact')}
          </button>
        </div>
      </div>
    </div>
  );
}
