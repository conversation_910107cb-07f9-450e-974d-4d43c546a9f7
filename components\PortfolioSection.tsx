import { useTranslations } from 'next-intl';
import { Project } from '../lib/data';
import { ProjectCard } from './ProjectCard';

interface PortfolioSectionProps {
  projects: Project[];
}

export function PortfolioSection({ projects }: PortfolioSectionProps) {
  const t = useTranslations();

  // Limit to maximum of 6 projects for the homepage
  const displayedProjects = projects.slice(0, 6);

  return (
    <section className="py-16 px-4 bg-base-200" data-testid="portfolio-section">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-base-content mb-4">
            {t('portfolio.title')}
          </h2>
          <p className="text-base-content/70 max-w-2xl mx-auto">
            {t('portfolio.subtitle')}
          </p>
        </div>
        
        <div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          data-testid="projects-grid"
        >
          {displayedProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
        
        {projects.length > 6 && (
          <div className="text-center">
            <button className="btn btn-primary">
              {t('portfolio.viewAll')}
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
