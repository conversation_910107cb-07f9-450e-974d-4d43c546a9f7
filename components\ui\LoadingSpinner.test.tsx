import { render, screen } from '@testing-library/react';
import { LoadingSpinner, LoadingOverlay } from './LoadingSpinner';
import * as animations from '../../lib/animations';

// Mock animation functions
jest.mock('../../lib/animations', () => ({
  craneLoadingAnimation: jest.fn(),
}));

describe('LoadingSpinner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading spinner with default props', () => {
    render(<LoadingSpinner />);
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByTestId('crane-animation')).toBeInTheDocument();
    expect(screen.getByTestId('crane-svg')).toBeInTheDocument();
  });

  it('should render with small size', () => {
    render(<LoadingSpinner size="sm" />);
    
    const craneAnimation = screen.getByTestId('crane-animation');
    expect(craneAnimation).toHaveClass('w-8', 'h-8');
  });

  it('should render with medium size (default)', () => {
    render(<LoadingSpinner size="md" />);
    
    const craneAnimation = screen.getByTestId('crane-animation');
    expect(craneAnimation).toHaveClass('w-12', 'h-12');
  });

  it('should render with large size', () => {
    render(<LoadingSpinner size="lg" />);
    
    const craneAnimation = screen.getByTestId('crane-animation');
    expect(craneAnimation).toHaveClass('w-16', 'h-16');
  });

  it('should apply custom className', () => {
    render(<LoadingSpinner className="custom-class" />);
    
    const loadingContainer = screen.getByTestId('loading-spinner');
    expect(loadingContainer).toHaveClass('custom-class');
  });

  it('should display loading text', () => {
    render(<LoadingSpinner />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should have crane SVG elements', () => {
    render(<LoadingSpinner />);
    
    const svg = screen.getByTestId('crane-svg');
    expect(svg).toBeInTheDocument();
    
    // Check for crane parts (rect elements for base, mast, jib, etc.)
    const rects = svg.querySelectorAll('rect');
    expect(rects.length).toBeGreaterThan(0);
  });

  it('should trigger crane loading animation on mount', () => {
    const animationsMock = jest.mocked(animations);

    render(<LoadingSpinner />);

    expect(animationsMock.craneLoadingAnimation).toHaveBeenCalledWith(
      expect.any(HTMLElement)
    );
  });
});

describe('LoadingOverlay', () => {
  it('should render when visible', () => {
    render(<LoadingOverlay isVisible={true} />);
    
    expect(screen.getByTestId('loading-overlay')).toBeInTheDocument();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('should not render when not visible', () => {
    render(<LoadingOverlay isVisible={false} />);
    
    expect(screen.queryByTestId('loading-overlay')).not.toBeInTheDocument();
  });

  it('should display custom message', () => {
    render(<LoadingOverlay isVisible={true} message="Please wait..." />);
    
    expect(screen.getByText('Please wait...')).toBeInTheDocument();
  });

  it('should display default message', () => {
    render(<LoadingOverlay isVisible={true} />);

    const loadingTexts = screen.getAllByText('Loading...');
    expect(loadingTexts.length).toBeGreaterThan(0);
  });

  it('should apply custom className', () => {
    render(<LoadingOverlay isVisible={true} className="custom-overlay" />);
    
    const overlay = screen.getByTestId('loading-overlay');
    expect(overlay).toHaveClass('custom-overlay');
  });

  it('should have correct overlay styling', () => {
    render(<LoadingOverlay isVisible={true} />);
    
    const overlay = screen.getByTestId('loading-overlay');
    expect(overlay).toHaveClass('fixed', 'inset-0', 'bg-base-100/80', 'backdrop-blur-sm', 'z-50');
  });

  it('should render large loading spinner in overlay', () => {
    render(<LoadingOverlay isVisible={true} />);
    
    const craneAnimation = screen.getByTestId('crane-animation');
    expect(craneAnimation).toHaveClass('w-16', 'h-16');
  });
});
