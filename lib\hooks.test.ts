import { renderHook, act } from '@testing-library/react';
import { useThemeStorage } from './hooks';

// Get access to the mocked localStorage
const mockLocalStorage = window.localStorage as jest.Mocked<Storage>;

describe('useThemeStorage', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should return default theme when localStorage is empty', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useThemeStorage('light'));
    
    expect(result.current[0]).toBe('light');
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('should return stored theme from localStorage', () => {
    mockLocalStorage.getItem.mockReturnValue('night');
    
    const { result } = renderHook(() => useThemeStorage('light'));
    
    expect(result.current[0]).toBe('night');
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('should update theme and save to localStorage', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useThemeStorage('light'));
    
    act(() => {
      result.current[1]('night');
    });
    
    expect(result.current[0]).toBe('night');
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme', 'night');
  });

  it('should handle invalid localStorage values', () => {
    // Suppress console.warn for this test
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    
    // Mock localStorage.getItem to throw an error
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });
    
    const { result } = renderHook(() => useThemeStorage('light'));
    
    // Should fall back to default theme when localStorage fails
    expect(result.current[0]).toBe('light');
    expect(consoleSpy).toHaveBeenCalledWith('Failed to read theme from localStorage:', expect.any(Error));
    
    // Restore console.warn
    consoleSpy.mockRestore();
  });
}); 