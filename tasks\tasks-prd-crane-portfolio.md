## Relevant Files

- `app/[locale]/layout.tsx` - Main app layout with next-intl integration, includes language/theme providers.
- `app/[locale]/page.tsx` - The main homepage component with internationalization support.
- `app/[locale]/contact/page.tsx` - The contact page component (to be created).
- `app/globals.css` - Global CSS file with DaisyUI theme configuration (night/light themes).
- `components/ui/Navbar.tsx` - The crane-themed navigation bar component (to be created).
- `components/ui/Navbar.test.tsx` - Unit tests for `Navbar.tsx` (to be created).
- `components/ThemeToggle.tsx` - Component for switching between light and dark themes.
- `components/ThemeToggle.test.tsx` - Unit tests for `ThemeToggle.tsx`.
- `components/LanguageToggle.tsx` - Component for switching between English and Romanian (to be created).
- `components/CraneCard.tsx` - Component for displaying individual crane information and images (to be created).
- `components/CraneCard.test.tsx` - Unit tests for `CraneCard.tsx` (to be created).
- `components/ContactForm.tsx` - The form component for handling inquiries (rental, purchase, sales) (to be created).
- `components/ContactForm.test.tsx` - Unit tests for `ContactForm.tsx` (to be created).
- `lib/data.ts` - To hold mock data for cranes, projects, and team members (to be created).
- `lib/hooks.ts` - Custom hooks including `useThemeStorage` for theme persistence.
- `lib/hooks.test.ts` - Unit tests for custom hooks.
- `lib/animations.ts` - Defines Motion animation variants (to be created).
- `messages/en.json` - Translation strings for English.
- `messages/ro.json` - Translation strings for Romanian.
- `middleware.ts` - The `next-intl` middleware for handling internationalized routing.
- `i18n.ts` - Next-intl configuration file.
- `jest.config.mjs` - Jest configuration for testing.
- `jest.setup.js` - Jest setup file with testing library imports.
- `next.config.ts` - Next.js configuration with next-intl plugin.
- `package.json` - Updated with new dependencies and test script.

### Notes

- Unit tests should be placed alongside the code files they are testing (e.g., `MyComponent.tsx` and `MyComponent.test.tsx` in the same directory).
- Use `npx jest [optional/path/to/test/file]` to run tests. Running without a path executes all tests found by the Jest configuration.
- Per the Tailwind CSS v4 documentation, it uses `@import "tailwindcss";` in the main CSS file and does not require a `tailwind.config.js` file for standard setup.
- **TDD Approach**: This task list is structured to encourage a Test-Driven Development (TDD) workflow. For components and logic, write a failing test first, then the code to make it pass. Visual and animation-related tasks should be verified manually or with visual regression testing tools.
- **Task 1.0 Complete**: All project setup and core functionality has been implemented and committed (commit: fccfeee).

## Tasks

- [x] 1.0 Project Setup and Core Functionality
  - [x] 1.1 Initialize a new Next.js 15+ project with the App Router (`npx create-next-app@latest`).
  - [x] 1.2 Install and configure Tailwind CSS 4+ and set up Jest for testing.
  - [x] 1.3 Add DaisyUI 5+ and configure themes in the main CSS file (e.g., `night` as default, `light` as alternative).
  - [x] 1.4 Install Motion One (`@motionone/react`) for animations.
  - [x] 1.5 Integrate `next-intl`, create `en.json`/`ro.json` files, and implement the middleware.
  - [x] 1.6 Implement Persistent Theme Toggle
    - [x] 1.6.1 Write a failing unit test for a custom hook (`useThemeStorage`) that handles theme persistence in `localStorage`.
    - [x] 1.6.2 Implement the `useThemeStorage` hook to make the test pass.
    - [x] 1.6.3 Write a failing test to ensure the `ThemeToggle` component renders correctly.
    - [x] 1.6.4 Build the `ThemeToggle` component using the hook and DaisyUI's `Theme Controller`.
- [ ] 2.0 Homepage and Content Display
  - [ ] 2.1 Develop the main layout for the homepage.
  - [ ] 2.2 Develop the `CraneCard` component
    - [ ] 2.2.1 Write a failing unit test for `CraneCard` to verify it correctly renders props (e.g., name, specs).
    - [ ] 2.2.2 Implement the `CraneCard` component structure to pass the test.
    - [ ] 2.2.3 Style the `CraneCard` using DaisyUI and populate the homepage with mock data from `lib/data.ts`.
  - [ ] 2.3 Develop Portfolio/Case Study components
    - [ ] 2.3.1 Write failing tests for `ProjectCard` and `PortfolioSection` components.
    - [ ] 2.3.2 Implement the components to pass the tests and style them.
  - [ ] 2.4 Develop Company/Team Info components
    - [ ] 2.4.1 Write failing tests for `TeamProfileCard` and `CompanyInfoSection` components.
    - [ ] 2.4.2 Implement the components to pass the tests and style them.
- [ ] 3.0 Implement Crane-Themed UI and Animations
  - [ ] 3.1 Develop the crane-themed `Navbar` component
    - [ ] 3.1.1 Write a failing unit test for the `Navbar` to ensure it renders navigation links and toggles.
    - [ ] 3.1.2 Implement the `Navbar` component's structure to pass the test.
    - [ ] 3.1.3 Style the `Navbar` to resemble a crane and integrate it into the main layout.
  - [ ] 3.2 Implement scroll-triggered animation for the `Navbar` "crane" to lift content using Motion One (verify visually).
  - [ ] 3.3 Add smooth page transitions using Motion One (verify visually).
  - [ ] 3.4 Implement hover effects on `CraneCard` and other interactive elements with Motion One (verify visually).
  - [ ] 3.5 Create parallax effect for the hero section using Motion One (verify visually).
  - [ ] 3.6 Design and implement a crane-themed loading animation using Motion One (verify visually).
- [ ] 4.0 Develop Contact Page and Inquiry Forms
  - [ ] 4.1 Create the layout for the contact page.
  - [ ] 4.2 Develop the `ContactForm` component
    - [ ] 4.2.1 Write failing tests for the `ContactForm`, covering input rendering, state changes, and validation feedback.
    - [ ] 4.2.2 Implement the `ContactForm` UI and client-side logic to pass the tests.
  - [ ] 4.3 Develop form submission functionality
    - [ ] 4.3.1 Write a failing integration test for the form submission server action, mocking any external services like email.
    - [ ] 4.3.2 Implement the server action to process and validate data, making the test pass.
    - [ ] 4.3.3 Connect the server action to the `ContactForm` and integrate a real email service.
  - [ ] 4.4 Add company contact information to the page.
- [ ] 5.0 Finalize, Optimize, and Deploy
  - [ ] 5.1 Ensure the entire website is fully responsive across all target devices.
  - [ ] 5.2 Optimize all images using the Next.js `Image` component.
  - [ ] 5.3 Conduct performance audit using Lighthouse and aim for a score of 90+.
  - [ ] 5.4 Improve Accessibility
    - [ ] 5.4.1 Run automated accessibility checks on all components (e.g., using `jest-axe`).
    - [ ] 5.4.2 Manually add required ARIA labels and semantic HTML.
  - [ ] 5.5 Perform cross-browser compatibility checks.
  - [ ] 5.6 Deploy the final application to Vercel. 