import { render, screen } from '@testing-library/react';
import { ProjectCard } from './ProjectCard';
import { Project } from '../lib/data';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'project.location': 'Location',
      'project.completed': 'Completed',
      'project.craneUsed': 'Crane Used',
      'project.client': 'Client',
      'project.viewDetails': 'View Project Details',
    };
    return translations[key] || key;
  },
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: unknown }) {
    return <div data-testid="mock-image" data-src={src} data-alt={alt} {...props} />;
  };
});

const mockProject: Project = {
  id: 'test-project-001',
  title: 'Test Construction Project',
  description: 'A comprehensive test project for validating project card functionality.',
  imageUrl: '/images/projects/test-project.jpg',
  location: 'Test City, Romania',
  completedDate: '2023-09-15',
  craneUsed: 'Liebherr LTM 1200-5.1',
  clientName: 'Test Development Group'
};

describe('ProjectCard', () => {
  it('renders project title correctly', () => {
    render(<ProjectCard project={mockProject} />);
    
    expect(screen.getByText('Test Construction Project')).toBeInTheDocument();
  });

  it('renders project description correctly', () => {
    render(<ProjectCard project={mockProject} />);
    
    expect(screen.getByText('A comprehensive test project for validating project card functionality.')).toBeInTheDocument();
  });

  it('renders project image with correct alt text', () => {
    render(<ProjectCard project={mockProject} />);

    const image = screen.getByTestId('mock-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('data-src', '/images/projects/test-project.jpg');
    expect(image).toHaveAttribute('data-alt', 'Test Construction Project');
  });

  it('renders project location correctly', () => {
    render(<ProjectCard project={mockProject} />);

    expect(screen.getByText(/Location/)).toBeInTheDocument();
    expect(screen.getByText('Test City, Romania')).toBeInTheDocument();
  });

  it('renders completion date correctly', () => {
    render(<ProjectCard project={mockProject} />);

    expect(screen.getByText(/Completed/)).toBeInTheDocument();
    expect(screen.getByText('September 2023')).toBeInTheDocument();
  });

  it('renders crane used correctly', () => {
    render(<ProjectCard project={mockProject} />);

    expect(screen.getByText(/Crane Used/)).toBeInTheDocument();
    expect(screen.getByText('Liebherr LTM 1200-5.1')).toBeInTheDocument();
  });

  it('renders client name correctly', () => {
    render(<ProjectCard project={mockProject} />);

    expect(screen.getByText(/Client/)).toBeInTheDocument();
    expect(screen.getByText('Test Development Group')).toBeInTheDocument();
  });

  it('renders view details button', () => {
    render(<ProjectCard project={mockProject} />);
    
    expect(screen.getByText('View Project Details')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View Project Details' })).toBeInTheDocument();
  });

  it('applies correct CSS classes for card structure', () => {
    render(<ProjectCard project={mockProject} />);
    
    const card = screen.getByTestId('project-card');
    expect(card).toHaveClass('card');
  });

  it('formats completion date correctly', () => {
    const projectWithDate = {
      ...mockProject,
      completedDate: '2023-12-25'
    };
    
    render(<ProjectCard project={projectWithDate} />);
    
    expect(screen.getByText('December 2023')).toBeInTheDocument();
  });
});
