import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { <PERSON> } from '../lib/data';

interface CraneCardProps {
  crane: Crane;
}

export function CraneCard({ crane }: CraneCardProps) {
  const t = useTranslations();

  return (
    <div className="card bg-base-100 shadow-xl" data-testid="crane-card">
      <figure className="px-4 pt-4">
        <Image
          src={crane.imageUrl}
          alt={crane.name}
          width={400}
          height={300}
          className="rounded-xl object-cover w-full h-48"
        />
      </figure>
      
      <div className="card-body">
        <h3 className="card-title text-base-content">
          {crane.name}
          <div 
            className={`badge ${crane.available ? 'badge-success' : 'badge-error'}`}
            data-testid="availability-status"
          >
            {crane.available ? t('crane.available') : t('crane.unavailable')}
          </div>
        </h3>
        
        <p className="text-sm text-base-content/70 mb-2">{crane.type}</p>
        
        <p className="text-base-content/80 mb-4">{crane.description}</p>
        
        <div className="mb-4">
          <h4 className="font-semibold text-base-content mb-2">
            {t('crane.specifications')}
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium text-base-content/70">
                {t('crane.capacity')}:
              </span>
              <span className="ml-1 text-base-content">
                {crane.specifications.capacity}
              </span>
            </div>
            <div>
              <span className="font-medium text-base-content/70">
                {t('crane.height')}:
              </span>
              <span className="ml-1 text-base-content">
                {crane.specifications.height}
              </span>
            </div>
            <div>
              <span className="font-medium text-base-content/70">
                {t('crane.boom')}:
              </span>
              <span className="ml-1 text-base-content">
                {crane.specifications.boom}
              </span>
            </div>
            <div>
              <span className="font-medium text-base-content/70">
                Year:
              </span>
              <span className="ml-1 text-base-content">
                {crane.specifications.year}
              </span>
            </div>
          </div>
        </div>
        
        {crane.price && crane.available && (
          <div className="mb-4">
            <div className="text-sm">
              <span className="font-medium text-base-content/70">Rental: </span>
              <span className="text-primary font-semibold">
                {crane.price.rental}
              </span>
            </div>
          </div>
        )}
        
        <div className="card-actions justify-end">
          <button className="btn btn-primary btn-sm">
            {t('home.viewDetails')}
          </button>
        </div>
      </div>
    </div>
  );
}
