import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ContactForm } from './ContactForm';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'rental': 'Rental Inquiry',
      'purchase': 'Purchase Inquiry',
      'sales': 'Sales Inquiry',
      'name': 'Name',
      'email': 'Email',
      'phone': 'Phone',
      'message': 'Message',
      'submit': 'Submit Inquiry',
      'required': 'Required field',
    };
    return translations[key] || key;
  },
}));

describe('ContactForm', () => {
  afterEach(() => {
    cleanup();
  });
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  it('should render all form fields', () => {
    render(<ContactForm />);
    
    // Check inquiry type radio buttons
    expect(screen.getByLabelText('Rental Inquiry')).toBeInTheDocument();
    expect(screen.getByLabelText('Purchase Inquiry')).toBeInTheDocument();
    expect(screen.getByLabelText('Sales Inquiry')).toBeInTheDocument();
    
    // Check input fields
    expect(screen.getByLabelText(/Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Phone/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Message/)).toBeInTheDocument();
    
    // Check submit button
    expect(screen.getByRole('button', { name: 'Submit Inquiry' })).toBeInTheDocument();
  });

  it('should have rental inquiry selected by default', () => {
    render(<ContactForm />);
    
    const rentalRadio = screen.getByLabelText('Rental Inquiry') as HTMLInputElement;
    expect(rentalRadio.checked).toBe(true);
  });

  it('should allow changing inquiry type', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);
    
    const purchaseRadio = screen.getByLabelText('Purchase Inquiry') as HTMLInputElement;
    const rentalRadio = screen.getByLabelText('Rental Inquiry') as HTMLInputElement;
    
    await user.click(purchaseRadio);
    
    expect(purchaseRadio.checked).toBe(true);
    expect(rentalRadio.checked).toBe(false);
  });

  it('should update input values when typed', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);
    
    const nameInput = screen.getByLabelText(/Name/) as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
    const phoneInput = screen.getByLabelText(/Phone/) as HTMLInputElement;
    const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;
    
    await user.type(nameInput, 'John Doe');
    await user.type(emailInput, '<EMAIL>');
    await user.type(phoneInput, '+40123456789');
    await user.type(messageInput, 'I need a crane for my project');
    
    expect(nameInput.value).toBe('John Doe');
    expect(emailInput.value).toBe('<EMAIL>');
    expect(phoneInput.value).toBe('+40123456789');
    expect(messageInput.value).toBe('I need a crane for my project');
  });

  it('should have empty form initially', () => {
    render(<ContactForm />);

    const nameInput = screen.getByLabelText(/Name/) as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
    const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;

    expect(nameInput.value).toBe('');
    expect(emailInput.value).toBe('');
    expect(messageInput.value).toBe('');
  });

  it('should show validation errors for required fields', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);

    const submitButton = screen.getByRole('button', { name: 'Submit Inquiry' });
    await user.click(submitButton);

    // Debug: Check what's actually in the DOM
    screen.debug();

    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Message is required')).toBeInTheDocument();
    });
  });

  it('should show email validation error for invalid email', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);
    
    const emailInput = screen.getByLabelText(/Email/);
    const submitButton = screen.getByRole('button', { name: 'Submit Inquiry' });

    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });
  });

  it('should disable submit button when form is submitting', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);
    
    // Fill required fields
    await user.type(screen.getByLabelText(/Name/), 'John Doe');
    await user.type(screen.getByLabelText(/Email/), '<EMAIL>');
    await user.type(screen.getByLabelText(/Message/), 'Test message');
    
    const submitButton = screen.getByRole('button', { name: 'Submit Inquiry' });
    await user.click(submitButton);
    
    // Button should be disabled during submission
    expect(submitButton).toBeDisabled();
  });

  it('should have correct form structure and accessibility', () => {
    render(<ContactForm />);
    
    // Check form element exists
    const form = screen.getByRole('form');
    expect(form).toBeInTheDocument();
    
    // Check required fields have required attribute
    expect(screen.getByLabelText(/Name/)).toBeRequired();
    expect(screen.getByLabelText(/Email/)).toBeRequired();
    expect(screen.getByLabelText(/Message/)).toBeRequired();

    // Check phone is not required
    expect(screen.getByLabelText(/Phone/)).not.toBeRequired();
  });

  it('should clear form after successful submission', async () => {
    const user = userEvent.setup();
    render(<ContactForm />);
    
    // Fill form
    await user.type(screen.getByLabelText(/Name/), 'John Doe');
    await user.type(screen.getByLabelText(/Email/), '<EMAIL>');
    await user.type(screen.getByLabelText(/Message/), 'Test message');
    
    const submitButton = screen.getByRole('button', { name: 'Submit Inquiry' });
    await user.click(submitButton);
    
    // Wait for form to be cleared (assuming successful submission)
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/Name/) as HTMLInputElement;
      const emailInput = screen.getByLabelText(/Email/) as HTMLInputElement;
      const messageInput = screen.getByLabelText(/Message/) as HTMLTextAreaElement;
      
      expect(nameInput.value).toBe('');
      expect(emailInput.value).toBe('');
      expect(messageInput.value).toBe('');
    }, { timeout: 3000 });
  });
});
